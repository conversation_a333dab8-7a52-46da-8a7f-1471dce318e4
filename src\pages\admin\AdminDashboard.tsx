
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, TrendingUp, Banknote, Building2, Bell, DollarSign } from "lucide-react";
import { LineChart, Line, ResponsiveContainer, XAxis, YAxis, Tooltip, Pie<PERSON>hart, Pie, Cell } from "recharts";
import { usePermissions } from "@/hooks/usePermission";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

const AdminDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    totalClients: 0,
    totalInvestments: 0,
    activeSchemes: 0,
    brokerInvestments: 0,
    companyFunds: null as any,
    recentTransactions: [] as any[],
    monthlyData: [] as any[],
    schemeDistribution: [] as any[]
  });
  const [loading, setLoading] = useState(true);

  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    if (!permissionsLoading && canAccess('dashboard_admin')) {
      fetchDashboardData();
    }
  }, [permissionsLoading, canAccess]);

  const fetchDashboardData = async () => {
    try {
      // Fetch clients count
      const { count: clientsCount } = await supabase
        .from('clients')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .eq('is_deleted', false);

      // Fetch total investments amount
      const { data: investments } = await supabase
        .from('investments')
        .select('amount')
        .eq('status', 'active');

      // Fetch active schemes count
      const { count: schemesCount } = await supabase
        .from('schemes')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Fetch broker investments
      const { data: brokerInvs } = await supabase
        .from('broker_investments')
        .select('amount_invested')
        .eq('status', 'active');

      // Fetch company funds
      const { data: funds } = await supabase
        .from('company_funds')
        .select('*')
        .single();

      // Fetch recent transactions
      const { data: transactions } = await supabase
        .from('investments')
        .select(`
          amount,
          created_at,
          clients(first_name, last_name),
          schemes(name)
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(5);

      // Calculate monthly investment data
      const { data: monthlyInvestments } = await supabase
        .from('investments')
        .select('amount, created_at')
        .eq('status', 'active')
        .gte('created_at', new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString());

      const monthlyData = Array.from({ length: 6 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() - (5 - i));
        const monthName = date.toLocaleDateString('en', { month: 'short' });
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
        
        const monthTotal = monthlyInvestments?.filter(inv => {
          const invDate = new Date(inv.created_at);
          return invDate >= monthStart && invDate <= monthEnd;
        }).reduce((sum, inv) => sum + inv.amount, 0) || 0;
        
        return { name: monthName, value: monthTotal };
      });

      // Scheme distribution
      const { data: schemeStats } = await supabase
        .from('investments')
        .select(`
          amount,
          schemes(name)
        `)
        .eq('status', 'active');

      const schemeGroups = schemeStats?.reduce((acc: any, inv) => {
        const schemeName = inv.schemes?.name || 'Others';
        acc[schemeName] = (acc[schemeName] || 0) + inv.amount;
        return acc;
      }, {}) || {};

      const totalAmount = Object.values(schemeGroups).reduce((sum: number, amount: any) => sum + amount, 0);
      const schemeDistribution = Object.entries(schemeGroups).map(([name, amount]: [string, any], index) => ({
        name,
        value: Math.round((amount / totalAmount) * 100),
        color: ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'][index % 5]
      }));

      setDashboardData({
        totalClients: clientsCount || 0,
        totalInvestments: investments?.reduce((sum, inv) => sum + inv.amount, 0) || 0,
        activeSchemes: schemesCount || 0,
        brokerInvestments: brokerInvs?.reduce((sum, inv) => sum + inv.amount_invested, 0) || 0,
        companyFunds: funds,
        recentTransactions: transactions || [],
        monthlyData,
        schemeDistribution
      });
    } catch (error: any) {
      toast.error('Failed to fetch dashboard data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) return `₹${(amount / 10000000).toFixed(1)}Cr`;
    if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
    return `₹${amount.toLocaleString()}`;
  };
  if (permissionsLoading) {
    return <div className="flex items-center justify-center h-64">Loading permissions...</div>;
  }

  if (!canAccess('dashboard_admin')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view dashboard.</p>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold text-primary">Admin Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's your investment overview</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            Notifications (3)
          </Button>
          <Button>Quick Actions</Button>
        </div>
      </header>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Clients</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : dashboardData.totalClients.toLocaleString()}</h2>
              <p className="text-green-600 text-sm">Active clients</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Investment</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : formatCurrency(dashboardData.totalInvestments)}</h2>
              <p className="text-green-600 text-sm">Active investments</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Available Funds</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : formatCurrency(dashboardData.companyFunds?.total_available || 0)}</h2>
              <p className="text-blue-600 text-sm">Company funds</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Banknote className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Broker Investment</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : formatCurrency(dashboardData.brokerInvestments)}</h2>
              <p className="text-orange-600 text-sm">With brokers</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Building2 className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Investment Trends</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dashboardData.monthlyData}>
                <XAxis dataKey="name" stroke="#888888" />
                <YAxis stroke="#888888" />
                <Tooltip formatter={(value) => [formatCurrency(value as number), 'Investment']} />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#8884d8"
                  strokeWidth={3}
                  dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Scheme Distribution</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={dashboardData.schemeDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name} ${value}%`}
                >
                  {dashboardData.schemeDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      {/* Recent Activity & Maturity Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Transactions</h3>
          <div className="space-y-4">
            {[
              { name: "John Doe", amount: "₹2,50,000", scheme: "Fixed Deposit", time: "2 hours ago", type: "investment" },
              { name: "Sarah Wilson", amount: "₹75,000", scheme: "Mutual Fund", time: "4 hours ago", type: "withdrawal" },
              { name: "Mike Johnson", amount: "₹1,20,000", scheme: "Bonds", time: "6 hours ago", type: "investment" },
              { name: "Lisa Brown", amount: "₹3,00,000", scheme: "Fixed Deposit", time: "1 day ago", type: "investment" },
            ].map((transaction, i) => (
              <div key={i} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${transaction.type === 'investment' ? 'bg-green-100' : 'bg-red-100'}`}>
                    <TrendingUp className={`h-4 w-4 ${transaction.type === 'investment' ? 'text-green-600' : 'text-red-600'}`} />
                  </div>
                  <div>
                    <p className="font-medium">{transaction.name}</p>
                    <p className="text-sm text-muted-foreground">{transaction.scheme}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-medium ${transaction.type === 'investment' ? 'text-green-600' : 'text-red-600'}`}>
                    {transaction.type === 'investment' ? '+' : '-'}{transaction.amount}
                  </p>
                  <p className="text-sm text-muted-foreground">{transaction.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Maturity Alerts</h3>
          <div className="space-y-4">
            {[
              { name: "Alice Cooper", amount: "₹5,50,000", scheme: "Fixed Deposit", maturityDate: "2024-01-15", daysLeft: 3 },
              { name: "Bob Smith", amount: "₹2,25,000", scheme: "Bonds", maturityDate: "2024-01-18", daysLeft: 6 },
              { name: "Carol White", amount: "₹3,75,000", scheme: "Fixed Deposit", maturityDate: "2024-01-22", daysLeft: 10 },
              { name: "David Brown", amount: "₹1,80,000", scheme: "Mutual Fund", maturityDate: "2024-01-25", daysLeft: 13 },
            ].map((alert, i) => (
              <div key={i} className="flex items-center justify-between p-3 border border-orange-200 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-orange-100 rounded-full">
                    <Bell className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="font-medium">{alert.name}</p>
                    <p className="text-sm text-muted-foreground">{alert.scheme} - {alert.amount}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-orange-600">{alert.daysLeft} days left</p>
                  <p className="text-sm text-muted-foreground">{alert.maturityDate}</p>
                </div>
              </div>
            ))}
          </div>
          <Button className="w-full mt-4" variant="outline">
            View All Alerts
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
