
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";

const ClientPortfolio = () => {
  const portfolioData = [
    { name: "Gold Plan", value: 50000, color: "#8B5CF6" },
    { name: "Silver Plan", value: 25000, color: "#06B6D4" },
    { name: "Matured", value: 100000, color: "#10B981" },
  ];

  const performanceData = [
    { month: "Jul", returns: 6200 },
    { month: "Aug", returns: 6200 },
    { month: "Sep", returns: 6200 },
    { month: "Oct", returns: 6200 },
    { month: "Nov", returns: 6200 },
    { month: "Dec", returns: 121200 }, // Including maturity
    { month: "Jan", returns: 6300 },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">My Portfolio</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Portfolio Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹1,75,000</div>
            <p className="text-xs text-muted-foreground">Current active investments</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹89,500</div>
            <p className="text-xs text-muted-foreground">Lifetime earnings</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Schemes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">Currently invested</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Payout</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹6,300</div>
            <p className="text-xs text-muted-foreground">Expected Feb 1, 2024</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Investment Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={portfolioData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ₹${value.toLocaleString()}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {portfolioData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `₹${value.toLocaleString()}`} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Returns Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => `₹${value.toLocaleString()}`} />
                <Legend />
                <Line type="monotone" dataKey="returns" stroke="#8B5CF6" strokeWidth={2} name="Monthly Returns (₹)" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Investments Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 border rounded-lg">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold">Gold Plan</h3>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Active</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Investment:</span>
                  <span className="font-semibold">₹50,000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Monthly Payout:</span>
                  <span className="font-semibold">₹4,200</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Maturity Date:</span>
                  <span className="font-semibold">Jan 15, 2025</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Returns:</span>
                  <span className="font-semibold text-green-600">₹10,080</span>
                </div>
              </div>
            </div>

            <div className="p-6 border rounded-lg">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold">Silver Plan</h3>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Active</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Investment:</span>
                  <span className="font-semibold">₹25,000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Monthly Payout:</span>
                  <span className="font-semibold">₹2,100</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Maturity Date:</span>
                  <span className="font-semibold">Feb 1, 2025</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Returns:</span>
                  <span className="font-semibold text-green-600">₹2,100</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientPortfolio;
