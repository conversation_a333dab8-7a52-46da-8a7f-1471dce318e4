// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://bwdakkytyiekkddcpaey.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ3ZGFra3l0eWlla2tkZGNwYWV5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4MDYzMzUsImV4cCI6MjA3MjM4MjMzNX0.lFRI_LEqvzgbsLFnEMDdRBXbDuK4hPEnfbjenkBWzuc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});