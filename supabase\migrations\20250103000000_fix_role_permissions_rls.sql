-- Fix RLS policies to avoid infinite recursion

-- Temporarily disable <PERSON><PERSON> on these tables to avoid recursion
ALTER TABLE public.roles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Admin can manage role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "Users can view their role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "Admin can manage roles" ON public.roles;
DROP POLICY IF EXISTS "Users can view roles" ON public.roles;
DROP POLICY IF EXISTS "Admin can manage permissions" ON public.permissions;
DROP POLICY IF EXISTS "Users can view permissions" ON public.permissions;

-- Re-enable RLS
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create simple policies that allow all authenticated users
-- You can restrict these later based on your security requirements

CREATE POLICY "Allow authenticated users" ON public.roles
FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users" ON public.permissions
FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users" ON public.role_permissions
FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users" ON public.users
FOR ALL USING (auth.uid() IS NOT NULL);