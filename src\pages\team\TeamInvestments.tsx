import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Search, Edit, Trash2, Calculator, TrendingUp } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

const TeamInvestments = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [investments, setInvestments] = useState<any[]>([]);
  const [clients, setClients] = useState<any[]>([]);
  const [schemes, setSchemes] = useState<any[]>([]);
  const [selectedScheme, setSelectedScheme] = useState<any>(null);
  const [formData, setFormData] = useState({
    client_id: "",
    scheme_id: "",
    amount: "",
    start_date: new Date().toISOString().split('T')[0],
    remarks: ""
  });
  const [calculations, setCalculations] = useState({
    end_date: "",
    monthly_interest: 0,
    total_expected_return: 0,
    maturity_amount: 0
  });

  useEffect(() => {
    fetchInvestments();
    fetchClients();
    fetchSchemes();
  }, []);

  useEffect(() => {
    if (selectedScheme && formData.amount && formData.start_date) {
      calculateInvestmentDetails();
    }
  }, [selectedScheme, formData.amount, formData.start_date]);

  const fetchInvestments = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          clients(first_name, last_name, email),
          schemes(name, monthly_interest_pct)
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const investmentsWithDetails = data?.map(investment => ({
        ...investment,
        client_name: `${investment.clients.first_name} ${investment.clients.last_name}`,
        scheme_name: investment.schemes.name,
        status_badge: investment.status === 'pending' ? 'Pending' :
          investment.status === 'active' ? 'Active' :
          investment.status === 'completed' ? 'Completed' :
            investment.status === 'closed' ? 'Closed' : 'Suspended'
      })) || [];

      setInvestments(investmentsWithDetails);
    } catch (error: any) {
      toast.error('Failed to fetch investments: ' + error.message);
    }
  };

  const fetchClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, first_name, last_name, email')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setClients(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch clients: ' + error.message);
    }
  };

  const fetchSchemes = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setSchemes(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch schemes: ' + error.message);
    }
  };

  const handleSchemeChange = (schemeId: string) => {
    const scheme = schemes.find(s => s.id === schemeId);
    setSelectedScheme(scheme);
    setFormData({ ...formData, scheme_id: schemeId });
  };

  const calculateInvestmentDetails = () => {
    if (!selectedScheme || !formData.amount || !formData.start_date) return;

    const amount = parseFloat(formData.amount);
    const startDate = new Date(formData.start_date);
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + selectedScheme.tenure_months);

    const monthlyInterest = (amount * selectedScheme.monthly_interest_pct) / 100;
    const totalExpectedReturn = monthlyInterest * selectedScheme.tenure_months;
    const maturityAmount = amount + totalExpectedReturn;

    setCalculations({
      end_date: endDate.toISOString().split('T')[0],
      monthly_interest: monthlyInterest,
      total_expected_return: totalExpectedReturn,
      maturity_amount: maturityAmount
    });
  };

  const generateInvestmentCode = () => {
    const prefix = 'INV';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!selectedScheme) {
        toast.error('Please select a scheme');
        return;
      }

      const amount = parseFloat(formData.amount);
      if (amount < selectedScheme.min_investment) {
        toast.error(`Minimum investment amount is ₹${selectedScheme.min_investment.toLocaleString()}`);
        return;
      }

      if (selectedScheme.max_investment && amount > selectedScheme.max_investment) {
        toast.error(`Maximum investment amount is ₹${selectedScheme.max_investment.toLocaleString()}`);
        return;
      }

      // Get current team member info
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      const { data: teamMember } = await supabase
        .from('team_members')
        .select('*')
        .eq('user_id', currentUser?.id)
        .single();

      if (!teamMember) {
        toast.error('Team member not found');
        return;
      }

      // Prepare team member data
      const teamMembersData = [{
        id: teamMember.id,
        name: `${teamMember.first_name} ${teamMember.last_name}`,
        refer_code: teamMember.refer_code,
        commission_amount: (amount * (selectedScheme.commission_pct || 0)) / 100
      }];

      const investmentData = {
        investment_code: generateInvestmentCode(),
        client_id: formData.client_id,
        scheme_id: formData.scheme_id,
        scheme_snapshot: JSON.stringify(selectedScheme),
        amount: amount,
        start_date: formData.start_date,
        end_date: calculations.end_date,
        monthly_interest: calculations.monthly_interest,
        total_expected_return: calculations.total_expected_return,
        maturity_amount: calculations.maturity_amount,
        team_members: JSON.stringify(teamMembersData),
        total_commission_pct: selectedScheme.commission_pct || 0,
        status: 'pending', // Team members create pending investments
        approved_by: null,
        approved_at: null
      };

      const { error } = await supabase
        .from('investments')
        .insert(investmentData);

      if (error) throw error;

      toast.success("Investment created successfully! Waiting for admin approval.");
      setIsAddDialogOpen(false);
      setFormData({
        client_id: "",
        scheme_id: "",
        amount: "",
        start_date: new Date().toISOString().split('T')[0],
        remarks: ""
      });
      setSelectedScheme(null);
      fetchInvestments();
    } catch (error: any) {
      toast.error('Failed to create investment: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const filteredInvestments = investments.filter(investment =>
    investment.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    investment.scheme_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    investment.investment_code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Client Investments</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Investment
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Investment</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="client_id">Client *</Label>
                    <Select value={formData.client_id} onValueChange={(value) => setFormData({ ...formData, client_id: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select client" />
                      </SelectTrigger>
                      <SelectContent>
                        {clients.map((client) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.first_name} {client.last_name} ({client.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="scheme_id">Scheme *</Label>
                    <Select value={formData.scheme_id} onValueChange={handleSchemeChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select scheme" />
                      </SelectTrigger>
                      <SelectContent>
                        {schemes.map((scheme) => (
                          <SelectItem key={scheme.id} value={scheme.id}>
                            {scheme.name} ({scheme.monthly_interest_pct}% monthly)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="amount">Investment Amount (₹) *</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={formData.amount}
                      onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                      placeholder="Enter investment amount"
                      required
                    />
                    {selectedScheme && (
                      <p className="text-xs text-gray-500 mt-1">
                        Min: ₹{selectedScheme.min_investment?.toLocaleString()}
                        {selectedScheme.max_investment && ` | Max: ₹${selectedScheme.max_investment?.toLocaleString()}`}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="start_date">Start Date *</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="remarks">Remarks</Label>
                    <Textarea
                      id="remarks"
                      value={formData.remarks}
                      onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
                      placeholder="Enter any additional remarks"
                      rows={3}
                    />
                  </div>
                </div>
                
                {/* Calculations Panel */}
                <div className="space-y-4">
                  {selectedScheme && formData.amount && (
                    <>
                      <Card className="p-4 bg-gradient-to-br from-blue-50 to-indigo-100">
                        <h4 className="font-semibold text-lg mb-3 flex items-center">
                          <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                          Scheme Details
                        </h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Scheme:</span>
                            <span className="font-medium">{selectedScheme.name}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Monthly Interest:</span>
                            <span className="font-medium text-green-600">{selectedScheme.monthly_interest_pct}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Tenure:</span>
                            <span className="font-medium">{selectedScheme.tenure_months} months</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Commission:</span>
                            <span className="font-medium">{selectedScheme.commission_pct || 0}%</span>
                          </div>
                        </div>
                      </Card>

                      <Card className="p-4 bg-gradient-to-br from-green-50 to-emerald-100">
                        <h4 className="font-semibold text-lg mb-3 flex items-center">
                          <Calculator className="h-5 w-5 mr-2 text-green-600" />
                          Investment Calculations
                        </h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Investment Amount:</span>
                            <span className="font-medium">₹{parseFloat(formData.amount || '0').toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Monthly Interest:</span>
                            <span className="font-medium text-green-600">₹{calculations.monthly_interest.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Total Returns:</span>
                            <span className="font-medium">₹{calculations.total_expected_return.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Maturity Amount:</span>
                            <span className="font-bold text-blue-600">₹{calculations.maturity_amount.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">End Date:</span>
                            <span className="font-medium">{calculations.end_date}</span>
                          </div>
                        </div>
                      </Card>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading || !selectedScheme}>
                  {loading ? "Creating..." : "Create Investment"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Client Investments</CardTitle>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search investments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Scheme</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>Maturity Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvestments.length > 0 ? (
                filteredInvestments.map((investment) => (
                  <TableRow key={investment.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.client_name}</p>
                        <p className="text-sm text-gray-500">{investment.clients.email}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.scheme_name}</p>
                        <p className="text-sm text-green-600">{investment.schemes.monthly_interest_pct}% monthly</p>
                      </div>
                    </TableCell>
                    <TableCell>₹{investment.amount.toLocaleString()}</TableCell>
                    <TableCell>{new Date(investment.start_date).toLocaleDateString()}</TableCell>
                    <TableCell>{new Date(investment.end_date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Badge variant={
                        investment.status === 'pending' ? 'outline' :
                        investment.status === 'active' ? 'default' :
                          investment.status === 'completed' ? 'secondary' :
                            'outline'
                      } className={
                        investment.status === 'pending' ? 'border-yellow-500 text-yellow-700' : ''
                      }>
                        {investment.status_badge}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => navigate(`/admin/investments/view/${investment.id}`)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    <p className="text-gray-500">No investments found</p>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamInvestments;