import { createContext, useContext, ReactNode } from 'react';
import { usePermissions } from '@/hooks/usePermission';

interface PermissionContextType {
  hasPermission: (module: string, action: 'view' | 'add' | 'edit' | 'delete') => boolean;
  canAccess: (module: string) => boolean;
  loading: boolean;
  userRole: string | null;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export const PermissionProvider = ({ children }: { children: ReactNode }) => {
  const permissionData = usePermissions();

  return (
    <PermissionContext.Provider value={permissionData}>
      {children}
    </PermissionContext.Provider>
  );
};

export const usePermissionContext = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermissionContext must be used within PermissionProvider');
  }
  return context;
};

