
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Heart, Users, Home, GraduationCap, Utensils } from "lucide-react";

const Charity = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-green-800 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Making a Difference Together
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            At Care Capital, we believe in giving back to the community. Join us in our mission to create 
            positive change and support those in need.
          </p>
          <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
            Support Our Cause
          </Button>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Mission</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              We are committed to creating sustainable impact through education, healthcare, 
              and community development initiatives that transform lives.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="text-center">
                <GraduationCap className="h-12 w-12 mx-auto mb-4 text-green-600" />
                <CardTitle>Education</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Supporting underprivileged children with quality education and scholarships
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Heart className="h-12 w-12 mx-auto mb-4 text-green-600" />
                <CardTitle>Healthcare</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Providing medical assistance and health awareness programs
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Home className="h-12 w-12 mx-auto mb-4 text-green-600" />
                <CardTitle>Housing</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Building homes and shelters for homeless families
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Utensils className="h-12 w-12 mx-auto mb-4 text-green-600" />
                <CardTitle>Nutrition</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Fighting hunger through food distribution programs
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Impact Numbers */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Our Impact</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">500+</div>
              <p className="text-muted-foreground">Children Educated</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">200+</div>
              <p className="text-muted-foreground">Families Supported</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">50+</div>
              <p className="text-muted-foreground">Homes Built</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">1000+</div>
              <p className="text-muted-foreground">Meals Served</p>
            </div>
          </div>
        </div>
      </section>

      {/* Donation Options */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">How You Can Help</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Make a Donation</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold mb-2">Bank Transfer</h3>
                      <p className="text-sm text-muted-foreground">Account: Care Capital Foundation</p>
                      <p className="text-sm text-muted-foreground">IFSC: HDFC0001234</p>
                      <p className="text-sm text-muted-foreground">Account No: **********</p>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">UPI Payment</h3>
                      <p className="text-sm text-muted-foreground">UPI ID: carecapital@upi</p>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">Online Donation</h3>
                      <Button className="w-full">Donate Now</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Volunteer With Us</CardTitle>
                </CardHeader>
                <CardContent>
                  <form className="space-y-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input id="name" placeholder="Enter your name" />
                    </div>
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" placeholder="Enter your email" />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Input id="phone" placeholder="Enter your phone" />
                    </div>
                    <div>
                      <Label htmlFor="interest">Area of Interest</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Education</option>
                        <option>Healthcare</option>
                        <option>Community Development</option>
                        <option>Event Organization</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="message">Message</Label>
                      <Textarea id="message" placeholder="Tell us about your interest in volunteering" rows={3} />
                    </div>
                    <Button className="w-full">Join as Volunteer</Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Activities */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Recent Activities</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>School Supply Drive</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Distributed books, stationery, and uniforms to 200 children in rural schools.
                </p>
                <p className="text-sm text-muted-foreground">January 2024</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Health Camp</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Organized free medical checkups and distributed medicines to 300 families.
                </p>
                <p className="text-sm text-muted-foreground">December 2023</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Community Kitchen</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Served hot meals to 500 underprivileged people during the winter season.
                </p>
                <p className="text-sm text-muted-foreground">November 2023</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Charity;
