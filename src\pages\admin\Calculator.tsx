
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calculator as CalculatorI<PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Percent } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

const Calculator = () => {
  const [schemes, setSchemes] = useState<any[]>([]);
  const [selectedScheme, setSelectedScheme] = useState<any>(null);
  const [principal, setPrincipal] = useState("");
  const [customRate, setCustomRate] = useState("");
  const [customTenure, setCustomTenure] = useState("");
  const [useCustom, setUseCustom] = useState(false);
  const [result, setResult] = useState<any>(null);
  const { canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    fetchSchemes();
  }, []);

  const fetchSchemes = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name');

      if (error) throw error;
      setSchemes(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch schemes: ' + error.message);
    }
  };

  const handleSchemeChange = (schemeId: string) => {
    if (schemeId === 'custom') {
      setUseCustom(true);
      setSelectedScheme(null);
    } else {
      const scheme = schemes.find(s => s.id === schemeId);
      setSelectedScheme(scheme);
      setUseCustom(false);
      if (scheme) {
        setPrincipal(scheme.min_investment?.toString() || '');
      }
    }
  };

  const calculateInvestment = () => {
    const amount = parseFloat(principal);
    if (!amount || amount <= 0) {
      toast.error('Please enter a valid investment amount');
      return;
    }

    let monthlyRate, tenureMonths, schemeName;

    if (useCustom) {
      const annualRate = parseFloat(customRate);
      const years = parseFloat(customTenure);
      if (!annualRate || !years) {
        toast.error('Please enter valid interest rate and tenure');
        return;
      }
      monthlyRate = annualRate / 100;
      tenureMonths = years * 12;
      schemeName = 'Custom Calculation';
    } else if (selectedScheme) {
      monthlyRate = selectedScheme.monthly_interest_pct / 100;
      tenureMonths = selectedScheme.tenure_months;
      schemeName = selectedScheme.name;

      // Validate investment limits
      if (amount < selectedScheme.min_investment) {
        toast.error(`Minimum investment for ${selectedScheme.name} is ₹${selectedScheme.min_investment.toLocaleString()}`);
        return;
      }
      if (selectedScheme.max_investment && amount > selectedScheme.max_investment) {
        toast.error(`Maximum investment for ${selectedScheme.name} is ₹${selectedScheme.max_investment.toLocaleString()}`);
        return;
      }
    } else {
      toast.error('Please select a scheme or use custom calculation');
      return;
    }

    // Calculate monthly interest
    const monthlyInterest = amount * monthlyRate;
    const totalInterest = monthlyInterest * tenureMonths;
    const maturityAmount = amount + totalInterest;

    // Calculate commission if scheme is selected
    const commission = selectedScheme ? (amount * (selectedScheme.commission_pct || 0)) / 100 : 0;

    // Calculate dates
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + tenureMonths);

    setResult({
      schemeName,
      principalAmount: amount,
      monthlyInterest,
      totalInterest,
      maturityAmount,
      tenureMonths,
      monthlyRate: monthlyRate * 100,
      startDate: startDate.toLocaleDateString(),
      endDate: endDate.toLocaleDateString(),
      commission,
      lockInPeriod: selectedScheme?.lock_in_period || 0
    });
  };


  if (permissionsLoading) {
    return <div>Loading...</div>; // Or a proper skeleton loader for the whole page
  }

  if (!canAccess('calculator')) {
    return (
      <div className="p-8 text-center text-red-600">
        <h2 className="text-xl font-bold">Access Denied</h2>
        <p>You do not have permission to use the calculator.</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-4xl font-bold text-primary">Investment Calculator</h1>
        <p className="text-muted-foreground">Calculate returns for different investment schemes</p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Calculator Input */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-full">
              <CalculatorIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold">Investment Calculator</h3>
          </div>

          <div className="space-y-6">
            <div className="space-y-2">
              <Label>Select Scheme</Label>
              <Select onValueChange={handleSchemeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a scheme or custom calculation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="custom">Custom Calculation</SelectItem>
                  {schemes.map((scheme) => (
                    <SelectItem key={scheme.id} value={scheme.id}>
                      {scheme.name} ({scheme.monthly_interest_pct}% monthly)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Investment Amount (₹) *</Label>
              <Input
                type="number"
                value={principal}
                onChange={(e) => setPrincipal(e.target.value)}
                placeholder="Enter investment amount"
              />
              {selectedScheme && (
                <p className="text-xs text-gray-500">
                  Min: ₹{selectedScheme.min_investment?.toLocaleString()}
                  {selectedScheme.max_investment && ` | Max: ₹${selectedScheme.max_investment?.toLocaleString()}`}
                </p>
              )}
            </div>

            {useCustom && (
              <>
                <div className="space-y-2">
                  <Label>Monthly Interest Rate (%)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={customRate}
                    onChange={(e) => setCustomRate(e.target.value)}
                    placeholder="2.0"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Investment Period (Years)</Label>
                  <Input
                    type="number"
                    value={customTenure}
                    onChange={(e) => setCustomTenure(e.target.value)}
                    placeholder="2"
                  />
                </div>
              </>
            )}

            {selectedScheme && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">Scheme Details</h4>
                <div className="space-y-1 text-sm text-blue-700">
                  <p>Monthly Interest: {selectedScheme.monthly_interest_pct}%</p>
                  <p>Tenure: {selectedScheme.tenure_months} months</p>
                  <p>Lock-in Period: {selectedScheme.lock_in_period || 0} months</p>
                  {selectedScheme.commission_pct > 0 && (
                    <p>Commission: {selectedScheme.commission_pct}%</p>
                  )}
                </div>
              </div>
            )}

            <Button onClick={calculateInvestment} className="w-full" size="lg">
              <CalculatorIcon className="h-4 w-4 mr-2" />
              Calculate Returns
            </Button>
          </div>
        </Card>

        {/* Results */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-green-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold">Calculation Results</h3>
          </div>

          {result ? (
            <div className="space-y-6">
              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-600 font-medium">Scheme</p>
                <p className="text-lg font-bold text-blue-800">{result.schemeName}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-600 font-medium">Investment Amount</p>
                  <p className="text-2xl font-bold text-green-800">
                    ₹{result.principalAmount?.toLocaleString()}
                  </p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <p className="text-sm text-purple-600 font-medium">Monthly Interest</p>
                  <p className="text-2xl font-bold text-purple-800">
                    ₹{result.monthlyInterest?.toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="text-sm text-primary font-medium">Maturity Amount</p>
                <p className="text-3xl font-bold text-primary">
                  ₹{result.maturityAmount?.toLocaleString()}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <p className="text-sm text-orange-600 font-medium">Total Interest</p>
                  <p className="text-xl font-bold text-orange-800">
                    ₹{result.totalInterest?.toLocaleString()}
                  </p>
                </div>
                {result.commission > 0 && (
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <p className="text-sm text-yellow-600 font-medium">Commission</p>
                    <p className="text-xl font-bold text-yellow-800">
                      ₹{result.commission?.toLocaleString()}
                    </p>
                  </div>
                )}
              </div>

              <div className="pt-4 border-t space-y-2">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Monthly Interest Rate:</span>
                  <span>{result.monthlyRate}%</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Investment Period:</span>
                  <span>{result.tenureMonths} months</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Start Date:</span>
                  <span>{result.startDate}</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Maturity Date:</span>
                  <span>{result.endDate}</span>
                </div>
                {result.lockInPeriod > 0 && (
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Lock-in Period:</span>
                    <span>{result.lockInPeriod} months</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <PieChart className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p>Enter investment details to see calculated returns</p>
            </div>
          )}
        </Card>
      </div>

      {/* Available Schemes */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold mb-4">Available Investment Schemes</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {schemes.map((scheme) => (
            <div key={scheme.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
              <h4 className="font-semibold text-primary mb-2">{scheme.name}</h4>
              <div className="space-y-1 text-sm text-muted-foreground mb-3">
                <div className="flex items-center space-x-1">
                  <Percent className="h-3 w-3" />
                  <span>Monthly Interest: {scheme.monthly_interest_pct}%</span>
                </div>
                <p>Tenure: {scheme.tenure_months} months</p>
                <p>Min Investment: ₹{scheme.min_investment?.toLocaleString()}</p>
                {scheme.max_investment && (
                  <p>Max Investment: ₹{scheme.max_investment?.toLocaleString()}</p>
                )}
                {scheme.lock_in_period > 0 && (
                  <p>Lock-in: {scheme.lock_in_period} months</p>
                )}
              </div>
              <Button
                size="sm"
                variant="outline"
                className="w-full"
                onClick={() => {
                  setSelectedScheme(scheme);
                  setUseCustom(false);
                  setPrincipal(scheme.min_investment?.toString() || '');
                }}
              >
                Calculate Returns
              </Button>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default Calculator;
