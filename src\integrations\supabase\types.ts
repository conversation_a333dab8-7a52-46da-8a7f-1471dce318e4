export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      activity_logs: {
        Row: {
          action: string
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          record_id: string | null
          table_name: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      alerts: {
        Row: {
          alert_date: string
          alert_type: Database["public"]["Enums"]["alert_type"]
          channel: Database["public"]["Enums"]["notification_channel"]
          client_id: string | null
          created_at: string | null
          created_by: string | null
          error_message: string | null
          id: string
          investment_id: string | null
          is_active: boolean | null
          is_deleted: boolean | null
          message: string
          sent_at: string | null
          status: string | null
          updated_at: string | null
          updated_by: string | null
          user_id: string | null
        }
        Insert: {
          alert_date: string
          alert_type: Database["public"]["Enums"]["alert_type"]
          channel: Database["public"]["Enums"]["notification_channel"]
          client_id?: string | null
          created_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          investment_id?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          message: string
          sent_at?: string | null
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Update: {
          alert_date?: string
          alert_type?: Database["public"]["Enums"]["alert_type"]
          channel?: Database["public"]["Enums"]["notification_channel"]
          client_id?: string | null
          created_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          investment_id?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          message?: string
          sent_at?: string | null
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "alerts_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "alerts_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
        ]
      }
      blogs: {
        Row: {
          author_id: string
          category: string | null
          content: string
          cover_image_url: string | null
          created_at: string | null
          created_by: string | null
          excerpt: string | null
          id: string
          is_deleted: boolean | null
          is_featured: boolean | null
          published_at: string | null
          seo_description: string | null
          seo_keywords: string | null
          seo_title: string | null
          slug: string
          status: Database["public"]["Enums"]["blog_status"] | null
          tags: string | null
          title: string
          updated_at: string | null
          updated_by: string | null
          view_count: number | null
        }
        Insert: {
          author_id: string
          category?: string | null
          content: string
          cover_image_url?: string | null
          created_at?: string | null
          created_by?: string | null
          excerpt?: string | null
          id?: string
          is_deleted?: boolean | null
          is_featured?: boolean | null
          published_at?: string | null
          seo_description?: string | null
          seo_keywords?: string | null
          seo_title?: string | null
          slug: string
          status?: Database["public"]["Enums"]["blog_status"] | null
          tags?: string | null
          title: string
          updated_at?: string | null
          updated_by?: string | null
          view_count?: number | null
        }
        Update: {
          author_id?: string
          category?: string | null
          content?: string
          cover_image_url?: string | null
          created_at?: string | null
          created_by?: string | null
          excerpt?: string | null
          id?: string
          is_deleted?: boolean | null
          is_featured?: boolean | null
          published_at?: string | null
          seo_description?: string | null
          seo_keywords?: string | null
          seo_title?: string | null
          slug?: string
          status?: Database["public"]["Enums"]["blog_status"] | null
          tags?: string | null
          title?: string
          updated_at?: string | null
          updated_by?: string | null
          view_count?: number | null
        }
        Relationships: []
      }
      broker_investments: {
        Row: {
          amount_invested: number
          broker_id: string
          created_at: string | null
          created_by: string | null
          end_date: string | null
          expected_return_pct: number | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          remarks: string | null
          start_date: string
          status: Database["public"]["Enums"]["investment_status"] | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          amount_invested: number
          broker_id: string
          created_at?: string | null
          created_by?: string | null
          end_date?: string | null
          expected_return_pct?: number | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          remarks?: string | null
          start_date: string
          status?: Database["public"]["Enums"]["investment_status"] | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          amount_invested?: number
          broker_id?: string
          created_at?: string | null
          created_by?: string | null
          end_date?: string | null
          expected_return_pct?: number | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          remarks?: string | null
          start_date?: string
          status?: Database["public"]["Enums"]["investment_status"] | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "broker_investments_broker_id_fkey"
            columns: ["broker_id"]
            isOneToOne: false
            referencedRelation: "brokers"
            referencedColumns: ["id"]
          },
        ]
      }
      broker_transactions: {
        Row: {
          amount: number
          broker_investment_id: string
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          reference_number: string | null
          remarks: string | null
          transaction_date: string | null
          transaction_type: Database["public"]["Enums"]["transaction_type"]
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          amount: number
          broker_investment_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          reference_number?: string | null
          remarks?: string | null
          transaction_date?: string | null
          transaction_type: Database["public"]["Enums"]["transaction_type"]
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          amount?: number
          broker_investment_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          reference_number?: string | null
          remarks?: string | null
          transaction_date?: string | null
          transaction_type?: Database["public"]["Enums"]["transaction_type"]
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "broker_transactions_broker_investment_id_fkey"
            columns: ["broker_investment_id"]
            isOneToOne: false
            referencedRelation: "broker_investments"
            referencedColumns: ["id"]
          },
        ]
      }
      brokers: {
        Row: {
          address: string | null
          broker_code: string
          broker_name: string
          commission_pct: number | null
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          license_number: string | null
          phone: string | null
          registration_number: string | null
          updated_at: string | null
          updated_by: string | null
          user_id: string | null
        }
        Insert: {
          address?: string | null
          broker_code: string
          broker_name: string
          commission_pct?: number | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          license_number?: string | null
          phone?: string | null
          registration_number?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Update: {
          address?: string | null
          broker_code?: string
          broker_name?: string
          commission_pct?: number | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          license_number?: string | null
          phone?: string | null
          registration_number?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      charity_donations: {
        Row: {
          amount: number
          cause: string | null
          created_at: string | null
          donation_method: string | null
          donor_email: string | null
          donor_name: string | null
          donor_phone: string | null
          id: string
          is_anonymous: boolean | null
          is_deleted: boolean | null
          message: string | null
          receipt_sent: boolean | null
          transaction_reference: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          cause?: string | null
          created_at?: string | null
          donation_method?: string | null
          donor_email?: string | null
          donor_name?: string | null
          donor_phone?: string | null
          id?: string
          is_anonymous?: boolean | null
          is_deleted?: boolean | null
          message?: string | null
          receipt_sent?: boolean | null
          transaction_reference?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          cause?: string | null
          created_at?: string | null
          donation_method?: string | null
          donor_email?: string | null
          donor_name?: string | null
          donor_phone?: string | null
          id?: string
          is_anonymous?: boolean | null
          is_deleted?: boolean | null
          message?: string | null
          receipt_sent?: boolean | null
          transaction_reference?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      client_documents: {
        Row: {
          client_id: string
          created_at: string | null
          created_by: string | null
          document_name: string | null
          document_type: string
          document_url: string
          id: string
          is_deleted: boolean | null
          updated_at: string | null
          updated_by: string | null
          verified: boolean | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          client_id: string
          created_at?: string | null
          created_by?: string | null
          document_name?: string | null
          document_type: string
          document_url: string
          id?: string
          is_deleted?: boolean | null
          updated_at?: string | null
          updated_by?: string | null
          verified?: boolean | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          client_id?: string
          created_at?: string | null
          created_by?: string | null
          document_name?: string | null
          document_type?: string
          document_url?: string
          id?: string
          is_deleted?: boolean | null
          updated_at?: string | null
          updated_by?: string | null
          verified?: boolean | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_documents_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          aadhar_number: string | null
          account_number: string | null
          address: string | null
          bank_name: string | null
          branch_name: string | null
          city: string | null
          client_code: string | null
          client_photo_url: string | null
          country: string | null
          created_at: string | null
          created_by: string | null
          email: string | null
          first_name: string
          id: string
          ifsc_code: string | null
          is_active: boolean | null
          is_deleted: boolean | null
          last_name: string | null
          mobile_number: string
          pan_card_number: string | null
          pincode: string | null
          referred_by: string | null
          state: string | null
          updated_at: string | null
          updated_by: string | null
          user_id: string | null
        }
        Insert: {
          aadhar_number?: string | null
          account_number?: string | null
          address?: string | null
          bank_name?: string | null
          branch_name?: string | null
          city?: string | null
          client_code?: string | null
          client_photo_url?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name: string
          id?: string
          ifsc_code?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          mobile_number: string
          pan_card_number?: string | null
          pincode?: string | null
          referred_by?: string | null
          state?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Update: {
          aadhar_number?: string | null
          account_number?: string | null
          address?: string | null
          bank_name?: string | null
          branch_name?: string | null
          city?: string | null
          client_code?: string | null
          client_photo_url?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name?: string
          id?: string
          ifsc_code?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          mobile_number?: string
          pan_card_number?: string | null
          pincode?: string | null
          referred_by?: string | null
          state?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clients_referred_by_fkey"
            columns: ["referred_by"]
            isOneToOne: false
            referencedRelation: "team_members"
            referencedColumns: ["id"]
          },
        ]
      }
      company_settings: {
        Row: {
          category: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          setting_key: string
          setting_type: string | null
          setting_value: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          setting_key: string
          setting_type?: string | null
          setting_value?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          setting_key?: string
          setting_type?: string | null
          setting_value?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      contact_inquiries: {
        Row: {
          assigned_to: string | null
          created_at: string | null
          email: string
          id: string
          inquiry_type: string | null
          is_deleted: boolean | null
          message: string
          name: string
          phone: string | null
          responded_at: string | null
          response: string | null
          status: string | null
          subject: string | null
          updated_at: string | null
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string | null
          email: string
          id?: string
          inquiry_type?: string | null
          is_deleted?: boolean | null
          message: string
          name: string
          phone?: string | null
          responded_at?: string | null
          response?: string | null
          status?: string | null
          subject?: string | null
          updated_at?: string | null
        }
        Update: {
          assigned_to?: string | null
          created_at?: string | null
          email?: string
          id?: string
          inquiry_type?: string | null
          is_deleted?: boolean | null
          message?: string
          name?: string
          phone?: string | null
          responded_at?: string | null
          response?: string | null
          status?: string | null
          subject?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      investment_payments: {
        Row: {
          amount: number
          created_at: string | null
          created_by: string | null
          due_date: string
          id: string
          investment_id: string
          is_active: boolean | null
          is_deleted: boolean | null
          month_number: number
          paid_amount: number | null
          paid_at: string | null
          payment_method: string | null
          remarks: string | null
          status: Database["public"]["Enums"]["payment_status"] | null
          transaction_reference: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          created_by?: string | null
          due_date: string
          id?: string
          investment_id: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          month_number: number
          paid_amount?: number | null
          paid_at?: string | null
          payment_method?: string | null
          remarks?: string | null
          status?: Database["public"]["Enums"]["payment_status"] | null
          transaction_reference?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          created_by?: string | null
          due_date?: string
          id?: string
          investment_id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          month_number?: number
          paid_amount?: number | null
          paid_at?: string | null
          payment_method?: string | null
          remarks?: string | null
          status?: Database["public"]["Enums"]["payment_status"] | null
          transaction_reference?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "investment_payments_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
        ]
      }
      investments: {
        Row: {
          amount: number
          client_id: string
          created_at: string | null
          created_by: string | null
          end_date: string
          id: string
          investment_code: string | null
          is_active: boolean | null
          is_deleted: boolean | null
          maturity_amount: number | null
          monthly_interest: number | null
          nominee_id: string | null
          scheme_id: string
          scheme_snapshot: Json
          start_date: string
          status: Database["public"]["Enums"]["investment_status"] | null
          team_members: Json | null
          total_commission_pct: number | null
          total_expected_return: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          amount: number
          client_id: string
          created_at?: string | null
          created_by?: string | null
          end_date: string
          id?: string
          investment_code?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          maturity_amount?: number | null
          monthly_interest?: number | null
          nominee_id?: string | null
          scheme_id: string
          scheme_snapshot: Json
          start_date: string
          status?: Database["public"]["Enums"]["investment_status"] | null
          team_members?: Json | null
          total_commission_pct?: number | null
          total_expected_return?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          amount?: number
          client_id?: string
          created_at?: string | null
          created_by?: string | null
          end_date?: string
          id?: string
          investment_code?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          maturity_amount?: number | null
          monthly_interest?: number | null
          nominee_id?: string | null
          scheme_id?: string
          scheme_snapshot?: Json
          start_date?: string
          status?: Database["public"]["Enums"]["investment_status"] | null
          team_members?: Json | null
          total_commission_pct?: number | null
          total_expected_return?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "investments_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_nominee_id_fkey"
            columns: ["nominee_id"]
            isOneToOne: false
            referencedRelation: "nominees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_scheme_id_fkey"
            columns: ["scheme_id"]
            isOneToOne: false
            referencedRelation: "schemes"
            referencedColumns: ["id"]
          },
        ]
      }
      new_permissions: {
        Row: {
          can_add: boolean | null
          can_delete: boolean | null
          can_edit: boolean | null
          can_view: boolean | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          module: string
          name: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          module: string
          name: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          module?: string
          name?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string | null
          first_name: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          last_name: string | null
          mobile: string | null
          role_id: string | null
          updated_at: string | null
          updated_by: string | null
          username: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name?: string | null
          id: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          mobile?: string | null
          role_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
          username?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          mobile?: string | null
          role_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      nominees: {
        Row: {
          address: string | null
          client_id: string
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          name: string
          phone: string | null
          relationship: string | null
          share_percentage: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          address?: string | null
          client_id: string
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          name: string
          phone?: string | null
          relationship?: string | null
          share_percentage?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          address?: string | null
          client_id?: string
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          name?: string
          phone?: string | null
          relationship?: string | null
          share_percentage?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nominees_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      permissions: {
        Row: {
          can_add: boolean | null
          can_delete: boolean | null
          can_edit: boolean | null
          can_view: boolean | null
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          module: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          module: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          module?: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string | null
          first_name: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          last_name: string | null
          mobile: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
          updated_by: string | null
          username: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name?: string | null
          id: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          mobile?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          updated_by?: string | null
          username?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          mobile?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          updated_by?: string | null
          username?: string | null
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          permission_id: string
          role_id: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          permission_id: string
          role_id: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          permission_id?: string
          role_id?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_permission_id_fkey"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "new_permissions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          name: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          name: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          name?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      schemes: {
        Row: {
          annual_interest_pct: number | null
          commission_pct: number | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          lock_in_period: number | null
          max_investment: number | null
          min_investment: number
          monthly_interest_pct: number
          name: string
          scheme_type: string | null
          tenure_months: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          annual_interest_pct?: number | null
          commission_pct?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          lock_in_period?: number | null
          max_investment?: number | null
          min_investment: number
          monthly_interest_pct: number
          name: string
          scheme_type?: string | null
          tenure_months: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          annual_interest_pct?: number | null
          commission_pct?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          lock_in_period?: number | null
          max_investment?: number | null
          min_investment?: number
          monthly_interest_pct?: number
          name?: string
          scheme_type?: string | null
          tenure_months?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      stock_training_inquiries: {
        Row: {
          created_at: string | null
          email: string
          experience_level: string | null
          follow_up_date: string | null
          id: string
          is_deleted: boolean | null
          message: string | null
          name: string
          phone: string
          preferred_schedule: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          experience_level?: string | null
          follow_up_date?: string | null
          id?: string
          is_deleted?: boolean | null
          message?: string | null
          name: string
          phone: string
          preferred_schedule?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          experience_level?: string | null
          follow_up_date?: string | null
          id?: string
          is_deleted?: boolean | null
          message?: string | null
          name?: string
          phone?: string
          preferred_schedule?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      team_members: {
        Row: {
          account_number: string | null
          address: string | null
          bank_name: string | null
          branch_name: string | null
          city: string | null
          commission_pct: number | null
          country: string | null
          created_at: string | null
          created_by: string | null
          email: string | null
          first_name: string
          id: string
          ifsc_code: string | null
          is_active: boolean | null
          is_deleted: boolean | null
          last_name: string | null
          phone: string | null
          pincode: string | null
          refer_code: string
          state: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_number?: string | null
          address?: string | null
          bank_name?: string | null
          branch_name?: string | null
          city?: string | null
          commission_pct?: number | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name: string
          id: string
          ifsc_code?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          phone?: string | null
          pincode?: string | null
          refer_code: string
          state?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_number?: string | null
          address?: string | null
          bank_name?: string | null
          branch_name?: string | null
          city?: string | null
          commission_pct?: number | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          first_name?: string
          id?: string
          ifsc_code?: string | null
          is_active?: boolean | null
          is_deleted?: boolean | null
          last_name?: string | null
          phone?: string | null
          pincode?: string | null
          refer_code?: string
          state?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean | null
          role_id: string
          updated_at: string | null
          updated_by: string | null
          user_id: string
        }
        Insert: {
          assigned_at?: string | null
          assigned_by?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          role_id: string
          updated_at?: string | null
          updated_by?: string | null
          user_id: string
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean | null
          role_id?: string
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
      get_user_role_new: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      has_permission_new: {
        Args: { module_name: string; permission_type: string }
        Returns: boolean
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_admin_new: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_team_member: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      alert_type:
        | "maturity"
        | "payment_reminder"
        | "investment_due"
        | "system_notification"
      blog_status: "draft" | "published" | "archived"
      investment_status: "active" | "completed" | "closed" | "suspended"
      notification_channel: "email" | "sms" | "whatsapp" | "push"
      payment_status: "pending" | "paid" | "overdue" | "cancelled"
      transaction_type:
        | "interest_payout"
        | "investment_allocation"
        | "withdrawal"
        | "commission_payment"
      user_role: "admin" | "team_member" | "client" | "broker"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      alert_type: [
        "maturity",
        "payment_reminder",
        "investment_due",
        "system_notification",
      ],
      blog_status: ["draft", "published", "archived"],
      investment_status: ["active", "completed", "closed", "suspended"],
      notification_channel: ["email", "sms", "whatsapp", "push"],
      payment_status: ["pending", "paid", "overdue", "cancelled"],
      transaction_type: [
        "interest_payout",
        "investment_allocation",
        "withdrawal",
        "commission_payment",
      ],
      user_role: ["admin", "team_member", "client", "broker"],
    },
  },
} as const
