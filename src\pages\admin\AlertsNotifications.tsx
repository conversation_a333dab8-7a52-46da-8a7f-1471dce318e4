
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, Send, Bell } from "lucide-react";

const AlertsNotifications = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const alerts = [
    { id: 1, client: "<PERSON>", type: "Maturity", message: "Investment maturing on 2024-02-15", status: "Pending", date: "2024-01-30" },
    { id: 2, client: "Jane Smith", type: "Payment", message: "Monthly payout due", status: "Sent", date: "2024-01-28" },
    { id: 3, client: "Mike Johnson", type: "Renewal", message: "Investment renewal reminder", status: "Pending", date: "2024-01-25" },
  ];

  const upcomingMaturities = [
    { client: "John Doe", scheme: "Gold Plan", amount: 50000, maturityDate: "2024-02-15" },
    { client: "Sarah Wilson", scheme: "Silver Plan", amount: 25000, maturityDate: "2024-02-20" },
    { client: "David Brown", scheme: "Platinum Plan", amount: 100000, maturityDate: "2024-02-25" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Alerts & Notifications</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Send Notification
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Send New Notification</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="client">Client</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select client" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="john">John Doe</SelectItem>
                    <SelectItem value="jane">Jane Smith</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="type">Notification Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="maturity">Maturity Alert</SelectItem>
                    <SelectItem value="payment">Payment Reminder</SelectItem>
                    <SelectItem value="renewal">Renewal Notice</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="message">Message</Label>
                <Textarea id="message" placeholder="Enter notification message" rows={4} />
              </div>
              <Button className="w-full" onClick={() => setIsAddDialogOpen(false)}>
                <Send className="mr-2 h-4 w-4" />
                Send Notification
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Maturities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingMaturities.map((maturity, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">{maturity.client}</h3>
                    <p className="text-sm text-muted-foreground">{maturity.scheme}</p>
                    <p className="text-sm text-muted-foreground">Due: {maturity.maturityDate}</p>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">₹{maturity.amount.toLocaleString()}</div>
                    <Button size="sm" className="mt-2">
                      <Bell className="mr-1 h-3 w-3" />
                      Alert
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Notifications</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {alerts.map((alert) => (
                  <TableRow key={alert.id}>
                    <TableCell>{alert.client}</TableCell>
                    <TableCell>{alert.type}</TableCell>
                    <TableCell>
                      <Badge variant={alert.status === "Sent" ? "default" : "secondary"}>
                        {alert.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{alert.date}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AlertsNotifications;
