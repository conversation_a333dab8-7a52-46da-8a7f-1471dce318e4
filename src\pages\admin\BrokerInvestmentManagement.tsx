import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { PlusCircle, Search, TrendingUp, DollarSign, Wallet, Target, Building2, ArrowUpRight, ArrowDownRight } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

interface CompanyFunds {
  id: string;
  total_available: number;
  total_invested: number;
  total_commission_paid: number;
  total_profit: number;
}

interface BrokerInvestment {
  id: string;
  investment_code: string;
  broker_id: string;
  amount_invested: number;
  start_date: string;
  end_date: string;
  expected_return_pct: number;
  maturity_amount: number;
  actual_return: number;
  profit_amount: number;
  status: string;
  remarks: string;
  brokers: { broker_name: string; broker_code: string };
}

interface Broker {
  id: string;
  broker_name: string;
  broker_code: string;
}

const BrokerInvestmentManagement = () => {
  const [funds, setFunds] = useState<CompanyFunds | null>(null);
  const [investments, setInvestments] = useState<BrokerInvestment[]>([]);
  const [brokers, setBrokers] = useState<Broker[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [maturityDialogOpen, setMaturityDialogOpen] = useState(false);
  const [selectedInvestment, setSelectedInvestment] = useState<BrokerInvestment | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [formData, setFormData] = useState({
    broker_id: "",
    amount_invested: 0,
    expected_return_pct: 0,
    start_date: "",
    end_date: "",
    remarks: "",
  });
  const [maturityData, setMaturityData] = useState({
    actual_return: 0,
    remarks: "",
  });
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    fetchData();
  }, []);

  const generateInvestmentCode = () => {
    const prefix = "BI";
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const fetchData = async () => {
    try {
      // Fetch company funds
      const { data: fundsData, error: fundsError } = await supabase
        .from('company_funds')
        .select('*')
        .single();

      if (fundsError) throw fundsError;
      setFunds(fundsData);

      // Fetch broker investments
      const { data: investmentsData, error: investmentsError } = await supabase
        .from('broker_investments')
        .select(`
          *,
          brokers(broker_name, broker_code)
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (investmentsError) throw investmentsError;
      setInvestments(investmentsData || []);

      // Fetch active brokers
      const { data: brokersData, error: brokersError } = await supabase
        .from('brokers')
        .select('id, broker_name, broker_code')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (brokersError) throw brokersError;
      setBrokers(brokersData || []);

    } catch (error: any) {
      toast.error('Failed to fetch data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const calculateAvailableFunds = () => {
    if (!funds) return 0;
    return funds.total_available;
  };

  const handleInvestmentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const availableFunds = calculateAvailableFunds();
    if (formData.amount_invested > availableFunds) {
      toast.error(`Insufficient funds. Available: ₹${availableFunds.toLocaleString()}`);
      return;
    }

    try {
      const maturityAmount = formData.amount_invested * (1 + formData.expected_return_pct / 100);
      
      const { data: investment, error } = await supabase
        .from('broker_investments')
        .insert({
          investment_code: generateInvestmentCode(),
          broker_id: formData.broker_id,
          amount_invested: formData.amount_invested,
          start_date: formData.start_date,
          end_date: formData.end_date || null,
          expected_return_pct: formData.expected_return_pct,
          maturity_amount: maturityAmount,
          remarks: formData.remarks,
        })
        .select()
        .single();

      if (error) throw error;

      // Update company funds
      await supabase
        .from('company_funds')
        .update({
          total_available: (funds?.total_available || 0) - formData.amount_invested,
          total_invested: (funds?.total_invested || 0) + formData.amount_invested,
        })
        .eq('id', funds?.id);

      // Record fund transaction
      await supabase
        .from('fund_transactions')
        .insert({
          transaction_type: 'investment',
          amount: formData.amount_invested,
          description: `Investment with broker`,
          balance_after: availableFunds - formData.amount_invested,
        });

      // Record broker transaction
      await supabase
        .from('broker_transactions')
        .insert({
          broker_investment_id: investment.id,
          transaction_type: 'investment_allocation',
          amount: formData.amount_invested,
          reference_number: generateInvestmentCode(),
          remarks: 'Investment allocation to broker',
        });

      toast.success('Investment created successfully');
      setDialogOpen(false);
      setFormData({
        broker_id: "",
        amount_invested: 0,
        expected_return_pct: 0,
        start_date: "",
        end_date: "",
        remarks: "",
      });
      fetchData();
    } catch (error: any) {
      toast.error('Failed to create investment: ' + error.message);
    }
  };

  const handleMaturitySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedInvestment) return;

    try {
      const profitAmount = maturityData.actual_return - selectedInvestment.amount_invested;
      
      const { error } = await supabase
        .from('broker_investments')
        .update({
          actual_return: maturityData.actual_return,
          profit_amount: profitAmount,
          status: 'completed',
          end_date: new Date().toISOString().split('T')[0],
          remarks: maturityData.remarks,
        })
        .eq('id', selectedInvestment.id);

      if (error) throw error;

      // Update company funds
      await supabase
        .from('company_funds')
        .update({
          total_available: (funds?.total_available || 0) + maturityData.actual_return,
          total_invested: (funds?.total_invested || 0) - selectedInvestment.amount_invested,
          total_profit: (funds?.total_profit || 0) + profitAmount,
        })
        .eq('id', funds?.id);

      // Record fund transaction
      await supabase
        .from('fund_transactions')
        .insert({
          transaction_type: 'profit',
          amount: maturityData.actual_return,
          description: `Maturity return from ${selectedInvestment.brokers.broker_name}`,
          broker_investment_id: selectedInvestment.id,
          balance_after: (funds?.total_available || 0) + maturityData.actual_return,
        });

      // Record broker transaction for maturity
      await supabase
        .from('broker_transactions')
        .insert({
          broker_investment_id: selectedInvestment.id,
          transaction_type: 'interest_payout',
          amount: maturityData.actual_return,
          reference_number: `MAT${Date.now()}`,
          remarks: 'Investment maturity payout',
        });

      toast.success('Investment matured successfully');
      setMaturityDialogOpen(false);
      setSelectedInvestment(null);
      setMaturityData({ actual_return: 0, remarks: "" });
      fetchData();
    } catch (error: any) {
      toast.error('Failed to process maturity: ' + error.message);
    }
  };

  const filteredInvestments = investments.filter(investment =>
    investment.brokers?.broker_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    investment.investment_code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (permissionsLoading) {
    return <div className="flex items-center justify-center h-64">Loading permissions...</div>;
  }

  if (!canAccess('broker_investments')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view broker investments.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Broker Investment Management</h1>
          <p className="text-gray-600 mt-1">Manage company investments with brokers</p>
        </div>
        {hasPermission('broker_investments', 'add') && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto shadow-sm">
                <PlusCircle className="h-4 w-4 mr-2" />
                New Investment
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle>Create New Investment</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleInvestmentSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="broker_id">Select Broker *</Label>
                  <Select value={formData.broker_id} onValueChange={(value) => setFormData({ ...formData, broker_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a broker" />
                    </SelectTrigger>
                    <SelectContent>
                      {brokers.map((broker) => (
                        <SelectItem key={broker.id} value={broker.id}>
                          {broker.broker_name} ({broker.broker_code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="amount_invested">Investment Amount *</Label>
                    <Input
                      id="amount_invested"
                      type="number"
                      value={formData.amount_invested}
                      onChange={(e) => setFormData({ ...formData, amount_invested: parseFloat(e.target.value) || 0 })}
                      placeholder="Enter amount"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Available: {formatCurrency(calculateAvailableFunds())}
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="expected_return_pct">Expected Return %</Label>
                    <Input
                      id="expected_return_pct"
                      type="number"
                      step="0.01"
                      value={formData.expected_return_pct}
                      onChange={(e) => setFormData({ ...formData, expected_return_pct: parseFloat(e.target.value) || 0 })}
                      placeholder="Enter return %"
                    />
                  </div>
                  <div>
                    <Label htmlFor="start_date">Start Date *</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="end_date">End Date</Label>
                    <Input
                      id="end_date"
                      type="date"
                      value={formData.end_date}
                      onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="remarks">Remarks</Label>
                  <Textarea
                    id="remarks"
                    value={formData.remarks}
                    onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
                    placeholder="Enter any remarks"
                    rows={3}
                  />
                </div>
                <div className="flex justify-end gap-3 pt-4">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Create Investment</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Company Funds Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Funds</CardTitle>
            <Wallet className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(calculateAvailableFunds())}</div>
            <p className="text-xs text-muted-foreground">After commissions</p>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invested</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(funds?.total_invested || 0)}</div>
            <p className="text-xs text-muted-foreground">Active investments</p>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(funds?.total_profit || 0)}</div>
            <p className="text-xs text-muted-foreground">Earned from investments</p>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Commission Paid</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(funds?.total_commission_paid || 0)}</div>
            <p className="text-xs text-muted-foreground">To team members</p>
          </CardContent>
        </Card>
      </div>

      {/* Investments Table */}
      <Card className="shadow-sm border-gray-200">
        <CardHeader className="bg-gray-50/50 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <Building2 className="h-5 w-5 text-primary" />
              Broker Investments
            </CardTitle>
            <div className="relative flex-1 md:grow-0">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search investments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full md:w-80"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="border-b border-gray-200">
                <TableHead className="font-semibold text-gray-900">Investment Code</TableHead>
                <TableHead className="font-semibold text-gray-900">Broker</TableHead>
                <TableHead className="font-semibold text-gray-900">Amount</TableHead>
                <TableHead className="font-semibold text-gray-900">Expected Return</TableHead>
                <TableHead className="font-semibold text-gray-900">Maturity Amount</TableHead>
                <TableHead className="font-semibold text-gray-900">Status</TableHead>
                <TableHead className="font-semibold text-gray-900">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                Array.from({ length: 5 }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                  </TableRow>
                ))
              ) : filteredInvestments.length > 0 ? (
                filteredInvestments.map((investment) => (
                  <TableRow key={investment.id} className="hover:bg-gray-50 border-b border-gray-100">
                    <TableCell className="font-medium">{investment.investment_code}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.brokers?.broker_name}</p>
                        <p className="text-sm text-gray-500">{investment.brokers?.broker_code}</p>
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(investment.amount_invested)}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {investment.expected_return_pct}%
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {investment.status === 'completed' && investment.actual_return ? 
                        formatCurrency(investment.actual_return) : 
                        (investment.maturity_amount ? 
                          formatCurrency(investment.maturity_amount) : 
                          formatCurrency(investment.amount_invested * (1 + (investment.expected_return_pct || 0) / 100))
                        )
                      }
                    </TableCell>
                    <TableCell>
                      <Badge variant={investment.status === 'active' ? 'default' : investment.status === 'completed' ? 'secondary' : 'outline'}>
                        {investment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {investment.status === 'active' && hasPermission('broker_investments', 'edit') && (
                        <Button
                          size="sm"
                          onClick={() => {
                            setSelectedInvestment(investment);
                            setMaturityData({ actual_return: investment.maturity_amount || 0, remarks: "" });
                            setMaturityDialogOpen(true);
                          }}
                        >
                          Mature
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-12">
                    <div className="flex flex-col items-center">
                      <Building2 className="h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold text-gray-700 mb-2">No Investments Found</h3>
                      <p className="text-gray-500">Create your first broker investment to get started.</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Maturity Dialog */}
      <Dialog open={maturityDialogOpen} onOpenChange={setMaturityDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Process Investment Maturity</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleMaturitySubmit} className="space-y-4">
            <div>
              <Label>Investment Code</Label>
              <Input value={selectedInvestment?.investment_code || ""} disabled />
            </div>
            <div>
              <Label>Original Amount</Label>
              <Input value={formatCurrency(selectedInvestment?.amount_invested || 0)} disabled />
            </div>
            <div>
              <Label>End Date</Label>
              <Input value={new Date().toISOString().split('T')[0]} disabled />
            </div>
            <div>
              <Label htmlFor="actual_return">Actual Return Amount *</Label>
              <Input
                id="actual_return"
                type="number"
                value={maturityData.actual_return}
                onChange={(e) => setMaturityData({ ...maturityData, actual_return: parseFloat(e.target.value) || 0 })}
                placeholder="Enter actual return amount"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Profit: {formatCurrency((maturityData.actual_return || 0) - (selectedInvestment?.amount_invested || 0))}
              </p>
            </div>
            <div>
              <Label htmlFor="maturity_remarks">Remarks</Label>
              <Textarea
                id="maturity_remarks"
                value={maturityData.remarks}
                onChange={(e) => setMaturityData({ ...maturityData, remarks: e.target.value })}
                placeholder="Enter maturity remarks"
                rows={3}
              />
            </div>
            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => setMaturityDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Process Maturity</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BrokerInvestmentManagement;