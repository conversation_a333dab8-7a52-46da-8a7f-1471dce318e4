import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Edit, Trash2, Shield, Settings } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";


interface Role {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
}

interface Permission {
  id: string;
  name: string;
  module: string;
  can_view: boolean;
  can_add: boolean;
  can_edit: boolean;
  can_delete: boolean;
  description: string;
}

const modules = [
  'clients', 'investments', 'schemes', 'team', 'brokers',
  'analytics', 'settings', 'reports', 'notifications'
];

const RolesManagement = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [permissionDialogOpen, setPermissionDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [rolePermissions, setRolePermissions] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
  });
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();
  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, []);

  const fetchRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setRoles(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch roles: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      const { data, error } = await supabase
        .from('permissions')
        .select('*')
        .eq('is_deleted', false)
        .eq('is_active', true)
        .order('module', { ascending: true });

      if (error) throw error;
      console.log('Fetched permissions:', data);
      setPermissions(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch permissions: ' + error.message);
    }
  };

  const fetchRolePermissions = async (roleId: string) => {
    try {
      const { data, error } = await supabase
        .from('role_permissions')
        .select('permission_id')
        .eq('role_id', roleId)
        .eq('is_deleted', false)
        .eq('is_active', true);

      if (error) throw error;
      setRolePermissions(data?.map(rp => rp.permission_id) || []);
    } catch (error: any) {
      toast.error('Failed to fetch role permissions: ' + error.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingRole) {
        const { error } = await supabase
          .from('roles')
          .update({
            name: formData.name,
            description: formData.description,
            updated_at: new Date().toISOString(),
          })
          .eq('id', editingRole.id);

        if (error) throw error;
        toast.success('Role updated successfully');
      } else {
        const { error } = await supabase
          .from('roles')
          .insert({
            name: formData.name,
            description: formData.description,
          });

        if (error) throw error;
        toast.success('Role created successfully');
      }

      setDialogOpen(false);
      setEditingRole(null);
      setFormData({ name: "", description: "" });
      fetchRoles();
    } catch (error: any) {
      toast.error('Failed to save role: ' + error.message);
    }
  };

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setFormData({
      name: role.name,
      description: role.description,
    });
    setDialogOpen(true);
  };

  const handleDelete = async (roleId: string) => {
    if (!confirm('Are you sure you want to delete this role?')) return;

    try {
      const { error } = await supabase
        .from('roles')
        .update({ is_deleted: true })
        .eq('id', roleId);

      if (error) throw error;
      toast.success('Role deleted successfully');
      fetchRoles();
    } catch (error: any) {
      toast.error('Failed to delete role: ' + error.message);
    }
  };

  const toggleStatus = async (roleId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('roles')
        .update({ is_active: !currentStatus })
        .eq('id', roleId);

      if (error) throw error;
      toast.success(`Role ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      fetchRoles();
    } catch (error: any) {
      toast.error('Failed to update role status: ' + error.message);
    }
  };

  const handleManagePermissions = (role: Role) => {
    setSelectedRole(role);
    fetchRolePermissions(role.id);
    setPermissionDialogOpen(true);
  };

  const handlePermissionUpdate = async () => {
    if (!selectedRole) return;

    try {
      // First, deactivate all existing role permissions
      await supabase
        .from('role_permissions')
        .update({ is_deleted: true, is_active: false })
        .eq('role_id', selectedRole.id);

      // Then, upsert selected permissions
      if (rolePermissions.length > 0) {
        const permissionData = rolePermissions.map(permissionId => ({
          role_id: selectedRole.id,
          permission_id: permissionId,
          is_active: true,
          is_deleted: false,
        }));

        const { error } = await supabase
          .from('role_permissions')
          .upsert(permissionData, {
            onConflict: 'role_id,permission_id'
          });

        if (error) throw error;
      }

      toast.success('Role permissions updated successfully');
      setPermissionDialogOpen(false);
    } catch (error: any) {
      toast.error('Failed to update role permissions: ' + error.message);
    }
  };

  const getPermissionActions = (permission: Permission) => {
    const actions = [];
    if (permission.can_view) actions.push('V');
    if (permission.can_add) actions.push('A');
    if (permission.can_edit) actions.push('E');
    if (permission.can_delete) actions.push('D');
    return actions.join(',');
  };

  if (permissionsLoading) {
    return <div className="flex items-center justify-center h-64">Loading permissions...</div>;
  }

  if (!canAccess('roles')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Roles Management</h1>
          <p className="text-gray-600 mt-1">Manage system roles and access levels</p>
        </div>
        {hasPermission('roles', 'add') && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button
                className="w-full sm:w-auto shadow-sm"
                onClick={() => {
                  setEditingRole(null);
                  setFormData({ name: "", description: "" });
                }}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Role
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="text-xl font-semibold">
                  {editingRole ? 'Edit Role' : 'Add New Role'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-5">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium">Role Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter role name"
                    className="h-10"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter role description"
                    rows={3}
                    className="resize-none"
                  />
                </div>
                <div className="flex flex-col-reverse sm:flex-row justify-end gap-3 pt-4">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)} className="w-full sm:w-auto">
                    Cancel
                  </Button>
                  <Button type="submit" className="w-full sm:w-auto">
                    {editingRole ? 'Update' : 'Create'} Role
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
        {/* Permission Assignment Dialog */}
        {hasPermission('permissions', 'edit') && (
          <Dialog open={permissionDialogOpen} onOpenChange={setPermissionDialogOpen}>
            <DialogContent className="max-w-4xl max-h-[85vh] overflow-hidden flex flex-col">
              <DialogHeader className="pb-4">
                <DialogTitle className="text-xl font-semibold">
                  Manage Permissions - <span className="text-primary">{selectedRole?.name}</span>
                </DialogTitle>
              </DialogHeader>
              <div className="flex-1 overflow-y-auto space-y-4 pr-2">
                <div className="mb-4 text-sm text-gray-600">
                  Total permissions loaded: {permissions.length}
                </div>
                {permissions.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No permissions found in database</p>
                  </div>
                ) : (
                  <>
                    {/* Show all permissions grouped by actual modules in database */}
                    {Array.from(new Set(permissions.map(p => p.module))).map((module) => {
                      const modulePermissions = permissions.filter(p => p.module === module);
                      console.log(`Module ${module} permissions:`, modulePermissions);

                      return (
                    <div key={module} className="border border-gray-200 rounded-lg p-4 bg-gray-50/50">
                      <h4 className="font-semibold mb-4 capitalize text-lg text-gray-800 flex items-center">
                        <Shield className="h-5 w-5 mr-2 text-primary" />
                        {module}
                      </h4>
                      <div className="grid gap-3">
                        {modulePermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                            <div className="flex items-center space-x-3 flex-1">
                              <Checkbox
                                id={permission.id}
                                checked={rolePermissions.includes(permission.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setRolePermissions([...rolePermissions, permission.id]);
                                  } else {
                                    setRolePermissions(rolePermissions.filter(id => id !== permission.id));
                                  }
                                }}
                                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                              />
                              <div className="flex-1 min-w-0">
                                <Label htmlFor={permission.id} className="font-medium text-gray-900 cursor-pointer">
                                  {permission.name}
                                </Label>
                                <p className="text-sm text-gray-600 mt-1 truncate">{permission.description}</p>
                              </div>
                            </div>
                            <div className="flex gap-1 ml-4">
                              {permission.can_view && <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">View</Badge>}
                              {permission.can_add && <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">Add</Badge>}
                              {permission.can_edit && <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">Edit</Badge>}
                              {permission.can_delete && <Badge variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">Delete</Badge>}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                      );
                    })}
                  </>
                )}
              </div>

              <div className="flex flex-col-reverse sm:flex-row justify-end gap-3 pt-4 border-t border-gray-200 mt-4">
                <Button variant="outline" onClick={() => setPermissionDialogOpen(false)} className="w-full sm:w-auto">
                  Cancel
                </Button>
                <Button onClick={handlePermissionUpdate} className="w-full sm:w-auto">
                  Update Permissions
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <Card className="shadow-sm border-gray-200">
        <CardHeader className="bg-gray-50/50 border-b border-gray-200">
          <CardTitle className="flex items-center gap-2 text-gray-900">
            <Shield className="h-5 w-5 text-primary" />
            System Roles
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* Mobile Card View */}
          <div className="block lg:hidden p-4">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-gray-200 h-20 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : roles.length > 0 ? (
              <div className="space-y-4">
                {roles.map((role) => (
                  <Card key={role.id} className="border border-gray-200 hover:border-gray-300 transition-colors">
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 text-lg">{role.name}</h3>
                          <p className="text-sm text-gray-600 mt-1">{role.description}</p>
                          <p className="text-xs text-gray-500 mt-2">
                            Created: {new Date(role.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge variant={role.is_active ? "default" : "secondary"} className="ml-3">
                          {role.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                        <div className="flex items-center space-x-3">
                          {hasPermission('roles', 'edit') && (
                            <Button size="sm" variant="outline" onClick={() => handleEdit(role)} className="h-8">
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                          )}
                          {hasPermission('permissions', 'edit') && (

                            <Button size="sm" variant="outline" onClick={() => handleManagePermissions(role)} className="h-8">
                              <Settings className="h-4 w-4 mr-1" />
                              Permissions
                            </Button>
                          )}
                        </div>

                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-600">Status:</span>
                            <Switch
                              checked={role.is_active}
                              onCheckedChange={() => toggleStatus(role.id, role.is_active)}
                              size="sm"
                            />
                          </div>
                          {hasPermission('roles', 'delete') && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(role.id)}
                              className="text-red-600 hover:text-red-700 hover:border-red-300 h-8 w-8 p-0"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-700 mb-2">No Roles Found</h3>
                <p className="text-gray-500">Create your first role to get started.</p>
              </div>
            )}
          </div>

          {/* Desktop Table View */}
          <div className="hidden lg:block">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-gray-200">
                  <TableHead className="font-semibold text-gray-900">Role Name</TableHead>
                  <TableHead className="font-semibold text-gray-900">Description</TableHead>
                  <TableHead className="font-semibold text-gray-900">Status</TableHead>
                  <TableHead className="font-semibold text-gray-900">Created</TableHead>
                  <TableHead className="font-semibold text-gray-900 text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 3 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><div className="animate-pulse bg-gray-200 h-4 w-24 rounded"></div></TableCell>
                      <TableCell><div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div></TableCell>
                      <TableCell><div className="animate-pulse bg-gray-200 h-6 w-16 rounded-full"></div></TableCell>
                      <TableCell><div className="animate-pulse bg-gray-200 h-4 w-20 rounded"></div></TableCell>
                      <TableCell><div className="animate-pulse bg-gray-200 h-8 w-32 rounded"></div></TableCell>
                    </TableRow>
                  ))
                ) : roles.length > 0 ? (
                  roles.map((role) => (
                    <TableRow key={role.id} className="hover:bg-gray-50 border-b border-gray-100">
                      <TableCell className="font-semibold text-gray-900">{role.name}</TableCell>
                      <TableCell className="text-gray-700 max-w-xs truncate">{role.description}</TableCell>
                      <TableCell>
                        <Badge variant={role.is_active ? "default" : "secondary"} className="font-medium">
                          {role.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-gray-600">
                        {new Date(role.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2">
                          <Button size="sm" variant="ghost" onClick={() => handleEdit(role)} className="h-8 w-8 p-0 hover:bg-blue-100">
                            <Edit className="h-4 w-4 text-blue-600" />
                          </Button>
                          <Button size="sm" variant="ghost" onClick={() => handleManagePermissions(role)} className="h-8 w-8 p-0 hover:bg-green-100">
                            <Settings className="h-4 w-4 text-green-600" />
                          </Button>
                          <Switch
                            checked={role.is_active}
                            onCheckedChange={() => toggleStatus(role.id, role.is_active)}
                            size="sm"
                          />
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDelete(role.id)}
                            className="h-8 w-8 p-0 hover:bg-red-100 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-12">
                      <div className="flex flex-col items-center">
                        <Shield className="h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-semibold text-gray-700 mb-2">No Roles Found</h3>
                        <p className="text-gray-500">Create your first role to get started.</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RolesManagement;