import { useState, useEffect } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>oot<PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { PlusCircle, Search, Edit, Trash2, MoreHorizontal, Building2, FileDown } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

interface Broker {
  id: string;
  broker_code: string;
  broker_name: string;
  email: string;
  phone: string;
  address: string;
  registration_number: string;
  license_number: string;
  commission_pct: number;
  is_active: boolean;
  created_at: string;
  totalInvestments?: number;
}

const BrokersManagement = () => {
  const [brokers, setBrokers] = useState<Broker[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingBroker, setEditingBroker] = useState<Broker | null>(null);
  const [brokerToDelete, setBrokerToDelete] = useState<Broker | null>(null);
  const [formData, setFormData] = useState({
    broker_name: "",
    email: "",
    phone: "",
    address: "",
    registration_number: "",
    license_number: "",
  });
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    fetchBrokers();
  }, []);

  const generateBrokerCode = () => {
    const prefix = "BRK";
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const fetchBrokers = async () => {
    try {
      const { data, error } = await supabase
        .from('brokers')
        .select(`
          *,
          broker_investments(id)
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const brokersWithStats = data?.map(broker => ({
        ...broker,
        totalInvestments: broker.broker_investments?.length || 0
      })) || [];

      setBrokers(brokersWithStats);
    } catch (error: any) {
      toast.error('Failed to fetch brokers: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingBroker) {
        const { error } = await supabase
          .from('brokers')
          .update({
            broker_name: formData.broker_name,
            email: formData.email,
            phone: formData.phone,
            address: formData.address,
            registration_number: formData.registration_number,
            license_number: formData.license_number,
            updated_at: new Date().toISOString(),
          })
          .eq('id', editingBroker.id);

        if (error) throw error;
        toast.success('Broker updated successfully');
      } else {
        const { error } = await supabase
          .from('brokers')
          .insert({
            broker_code: generateBrokerCode(),
            broker_name: formData.broker_name,
            email: formData.email,
            phone: formData.phone,
            address: formData.address,
            registration_number: formData.registration_number,
            license_number: formData.license_number,
          });

        if (error) throw error;
        toast.success('Broker created successfully');
      }

      setDialogOpen(false);
      setEditingBroker(null);
      setFormData({
        broker_name: "",
        email: "",
        phone: "",
        address: "",
        registration_number: "",
        license_number: "",
      });
      fetchBrokers();
    } catch (error: any) {
      toast.error('Failed to save broker: ' + error.message);
    }
  };

  const handleEdit = (broker: Broker) => {
    setEditingBroker(broker);
    setFormData({
      broker_name: broker.broker_name,
      email: broker.email,
      phone: broker.phone,
      address: broker.address,
      registration_number: broker.registration_number,
      license_number: broker.license_number,
    });
    setDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!brokerToDelete) return;

    try {
      const { error } = await supabase
        .from('brokers')
        .update({ is_deleted: true })
        .eq('id', brokerToDelete.id);

      if (error) throw error;
      toast.success(`Broker "${brokerToDelete.broker_name}" has been deleted.`);
      setBrokers(brokers.filter(b => b.id !== brokerToDelete.id));
      setBrokerToDelete(null);
    } catch (error: any) {
      toast.error('Failed to delete broker: ' + error.message);
    }
  };

  const toggleStatus = async (brokerId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('brokers')
        .update({ is_active: !currentStatus })
        .eq('id', brokerId);

      if (error) throw error;
      toast.success(`Broker ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      fetchBrokers();
    } catch (error: any) {
      toast.error('Failed to update broker status: ' + error.message);
    }
  };

  const filteredBrokers = brokers.filter(broker =>
    broker.broker_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    broker.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    broker.broker_code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const SkeletonRow = () => (
    <TableRow>
      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
      <TableCell><Skeleton className="h-4 w-12" /></TableCell>
      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
      <TableCell><Skeleton className="h-8 w-8" /></TableCell>
    </TableRow>
  );

  if (permissionsLoading) {
    return <div className="flex items-center justify-center h-64">Loading permissions...</div>;
  }

  if (!canAccess('brokers')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view brokers.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Brokers Management</h1>
          <p className="text-gray-600 mt-1">Manage broker partnerships and commissions</p>
        </div>
        {hasPermission('brokers', 'add') && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button
                className="w-full sm:w-auto shadow-sm"
                onClick={() => {
                  setEditingBroker(null);
                  setFormData({
                    broker_name: "",
                    email: "",
                    phone: "",
                    address: "",
                    registration_number: "",
                    license_number: "",
                  });
                }}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Broker
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle className="text-xl font-semibold">
                  {editingBroker ? 'Edit Broker' : 'Add New Broker'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-2">
                    <Label htmlFor="broker_name" className="text-sm font-medium">Broker Name *</Label>
                    <Input
                      id="broker_name"
                      value={formData.broker_name}
                      onChange={(e) => setFormData({ ...formData, broker_name: e.target.value })}
                      placeholder="Enter broker name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-sm font-medium">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      placeholder="Enter email"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-sm font-medium">Phone *</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      placeholder="Enter phone number"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="registration_number" className="text-sm font-medium">Registration Number</Label>
                    <Input
                      id="registration_number"
                      value={formData.registration_number}
                      onChange={(e) => setFormData({ ...formData, registration_number: e.target.value })}
                      placeholder="Enter registration number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="license_number" className="text-sm font-medium">License Number</Label>
                    <Input
                      id="license_number"
                      value={formData.license_number}
                      onChange={(e) => setFormData({ ...formData, license_number: e.target.value })}
                      placeholder="Enter license number"
                    />
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="address" className="text-sm font-medium">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      placeholder="Enter broker address"
                      rows={3}
                    />
                  </div>
                </div>
                <div className="flex flex-col-reverse sm:flex-row justify-end gap-3 pt-4">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)} className="w-full sm:w-auto">
                    Cancel
                  </Button>
                  <Button type="submit" className="w-full sm:w-auto">
                    {editingBroker ? 'Update' : 'Create'} Broker
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <Card className="shadow-sm border-gray-200">
        <CardHeader className="bg-gray-50/50 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <Building2 className="h-5 w-5 text-primary" />
              All Brokers
            </CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative flex-1 md:grow-0">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search brokers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full md:w-80"
                />
              </div>
              {hasPermission('brokers', 'view') && (
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-gray-200">
                  <TableHead className="font-semibold text-gray-900">Broker Details</TableHead>
                  <TableHead className="font-semibold text-gray-900">Contact</TableHead>
                  <TableHead className="font-semibold text-gray-900">Commission</TableHead>
                  <TableHead className="font-semibold text-gray-900">Investments</TableHead>
                  <TableHead className="font-semibold text-gray-900">Status</TableHead>
                  <TableHead className="font-semibold text-gray-900 text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 5 }).map((_, i) => <SkeletonRow key={i} />)
                ) : filteredBrokers.length > 0 ? (
                  filteredBrokers.map((broker) => (
                    <TableRow key={broker.id} className="hover:bg-gray-50 border-b border-gray-100">
                      <TableCell>
                        <div>
                          <p className="font-semibold text-gray-900">{broker.broker_name}</p>
                          <p className="text-sm text-gray-500">{broker.broker_code}</p>
                          {broker.registration_number && (
                            <p className="text-xs text-gray-400">Reg: {broker.registration_number}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm font-medium text-gray-700">{broker.email}</p>
                          <p className="text-sm text-gray-500">{broker.phone}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          {broker.commission_pct}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{broker.totalInvestments}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant={broker.is_active ? "default" : "secondary"} className="font-medium">
                            {broker.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {hasPermission('brokers', 'edit') && (
                                <DropdownMenuItem onClick={() => handleEdit(broker)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                              )}
                              {hasPermission('brokers', 'edit') && (
                                <DropdownMenuItem onClick={() => toggleStatus(broker.id, broker.is_active)}>
                                  <Switch className="mr-2 h-4 w-4" />
                                  {broker.is_active ? 'Deactivate' : 'Activate'}
                                </DropdownMenuItem>
                              )}
                              {hasPermission('brokers', 'delete') && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => setBrokerToDelete(broker)}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-12">
                      <div className="flex flex-col items-center">
                        <Building2 className="h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-semibold text-gray-700 mb-2">No Brokers Found</h3>
                        <p className="text-gray-500">Create your first broker to get started.</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={!!brokerToDelete} onOpenChange={() => setBrokerToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the broker "{brokerToDelete?.broker_name}" as deleted. This is a soft delete and the data can be recovered.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Yes, Delete Broker
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default BrokersManagement;