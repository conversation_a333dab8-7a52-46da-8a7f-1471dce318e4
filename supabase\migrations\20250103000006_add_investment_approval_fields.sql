-- Add approval fields to investments table

-- Add new columns to investments table
ALTER TABLE public.investments 
ADD COLUMN IF NOT EXISTS approved_by uuid REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS approved_at timestamp with time zone;

-- Update default status to pending (assuming pending already exists)
ALTER TABLE public.investments 
ALTER COLUMN status SET DEFAULT 'pending'::investment_status;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_investments_approved_by ON public.investments(approved_by);
CREATE INDEX IF NOT EXISTS idx_investments_approved_at ON public.investments(approved_at);

-- Create function to handle investment approval
CREATE OR REPLACE FUNCTION public.approve_investment(investment_id uuid, approver_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    investment_record RECORD;
    tenure_months integer;
    scheme_name text;
    i integer;
BEGIN
    -- Get investment details
    SELECT * INTO investment_record FROM investments WHERE id = investment_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Investment not found';
    END IF;
    
    IF investment_record.status != 'pending' THEN
        RAISE EXCEPTION 'Investment is not in pending status';
    END IF;
    
    -- Debug: Check scheme_snapshot content
    RAISE NOTICE 'scheme_snapshot: %', investment_record.scheme_snapshot;
    
    -- Parse scheme snapshot with COALESCE for safety
    tenure_months := COALESCE((investment_record.scheme_snapshot->>'tenure_months')::integer, 12);
    scheme_name := COALESCE(investment_record.scheme_snapshot->>'name', 'Unknown Scheme');
    
    -- Check if tenure_months is valid
    IF tenure_months <= 0 THEN
        RAISE EXCEPTION 'Invalid tenure_months: %', tenure_months;
    END IF;
    
    -- Update investment status
    UPDATE investments 
    SET 
        status = 'active',
        approved_by = approver_id,
        approved_at = now()
    WHERE id = investment_id;
    
    -- Create payment schedule using loop
    FOR i IN 1..tenure_months LOOP
        INSERT INTO investment_payments (
            investment_id,
            month_number,
            due_date,
            amount,
            status
        ) VALUES (
            investment_id,
            i,
            (investment_record.start_date::date + (i || ' months')::interval)::date,
            investment_record.monthly_interest,
            'pending'
        );
    END LOOP;
    
    -- Create transaction record
    INSERT INTO transactions (
        transaction_code,
        investment_id,
        client_id,
        transaction_type,
        amount,
        description,
        reference_number,
        payment_method,
        status
    ) VALUES (
        'TXN' || extract(epoch from now())::bigint,
        investment_id,
        investment_record.client_id,
        'investment_allocation',
        investment_record.amount,
        'Investment in ' || scheme_name,
        investment_record.investment_code,
        'bank_transfer',
        'completed'
    );
    
    -- Update company funds after approval
    DECLARE
        commission_amount NUMERIC := 0;
        net_amount NUMERIC;
    BEGIN
        -- Calculate commission
        commission_amount := (investment_record.amount * COALESCE(investment_record.total_commission_pct, 0)) / 100;
        net_amount := investment_record.amount - commission_amount;
        
        -- Update company funds
        UPDATE public.company_funds SET
            total_available = total_available + net_amount,
            total_commission_paid = total_commission_paid + commission_amount,
            last_updated = now()
        WHERE id = (SELECT id FROM company_funds LIMIT 1);
        
        -- Record fund transaction
        INSERT INTO public.fund_transactions (transaction_type, amount, description, balance_after)
        VALUES (
            'deposit',
            net_amount,
            'Client investment: ' || investment_record.investment_code,
            (SELECT total_available FROM company_funds LIMIT 1)
        );
    END;
END;
$$;