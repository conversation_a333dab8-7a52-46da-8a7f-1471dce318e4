import { ReactNode } from 'react';
import { usePermissionContext, Permission } from '@/contexts/PermissionContext';
import { Card } from '@/components/ui/card';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermission?: keyof Permission;
  fallback?: ReactNode;
}

export const ProtectedRoute = ({ 
  children, 
  requiredPermission, 
  fallback 
}: ProtectedRouteProps) => {
  const { hasPermission } = usePermissionContext();

  if (requiredPermission && !hasPermission(requiredPermission)) {
    return fallback || (
      <Card className="p-6 text-center">
        <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
        <p className="text-muted-foreground">You don't have permission to view this page.</p>
      </Card>
    );
  }

  return <>{children}</>;
};