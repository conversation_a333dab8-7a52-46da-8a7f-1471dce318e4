import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Edit, Trash2, Key, Shield } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

interface Permission {
  id: string;
  name: string;
  module: string;
  can_view: boolean;
  can_add: boolean;
  can_edit: boolean;
  can_delete: boolean;
  description: string;
  is_active: boolean;
  created_at: string;
}

interface Role {
  id: string;
  name: string;
}

const modules = [
  'clients', 'investments', 'schemes', 'team', 'brokers',
  'analytics', 'settings', 'reports', 'notifications'
];

const PermissionsManagement = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    module: "",
    can_view: false,
    can_add: false,
    can_edit: false,
    can_delete: false,
    description: "",
  });
  const [customModule, setCustomModule] = useState("");
  const [showCustomModule, setShowCustomModule] = useState(false);
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      const { data, error } = await supabase
        .from('permissions')
        .select('*')
        .eq('is_deleted', false)
        .order('module', { ascending: true });

      if (error) throw error;
      setPermissions(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch permissions: ' + error.message);
    } finally {
      setLoading(false);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingPermission) {
        const { error } = await supabase
          .from('permissions')
          .update({
            name: formData.name,
            module: formData.module,
            can_view: formData.can_view,
            can_add: formData.can_add,
            can_edit: formData.can_edit,
            can_delete: formData.can_delete,
            description: formData.description,
            updated_at: new Date().toISOString(),
          })
          .eq('id', editingPermission.id);

        if (error) throw error;
        toast.success('Permission updated successfully');
      } else {
        const { error } = await supabase
          .from('permissions')
          .insert({
            name: formData.name,
            module: formData.module,
            can_view: formData.can_view,
            can_add: formData.can_add,
            can_edit: formData.can_edit,
            can_delete: formData.can_delete,
            description: formData.description,
          });

        if (error) throw error;
        toast.success('Permission created successfully');
      }

      setDialogOpen(false);
      setEditingPermission(null);
      resetForm();
      fetchPermissions();
    } catch (error: any) {
      toast.error('Failed to save permission: ' + error.message);
    }
  };



  const resetForm = () => {
    setFormData({
      name: "",
      module: "",
      can_view: false,
      can_add: false,
      can_edit: false,
      can_delete: false,
      description: "",
    });
    setCustomModule("");
    setShowCustomModule(false);
  };

  const handleModuleChange = (value: string) => {
    if (value === "custom") {
      setShowCustomModule(true);
      setFormData({ ...formData, module: "" });
    } else {
      setShowCustomModule(false);
      setFormData({ ...formData, module: value });
    }
  };

  const handleCustomModuleSubmit = () => {
    if (customModule.trim()) {
      setFormData({ ...formData, module: customModule.trim() });
      setShowCustomModule(false);
    }
  };

  const handleEdit = (permission: Permission) => {
    setEditingPermission(permission);
    setFormData({
      name: permission.name,
      module: permission.module,
      can_view: permission.can_view,
      can_add: permission.can_add,
      can_edit: permission.can_edit,
      can_delete: permission.can_delete,
      description: permission.description,
    });
    setDialogOpen(true);
  };

  const handleDelete = async (permissionId: string) => {
    if (!confirm('Are you sure you want to delete this permission?')) return;

    try {
      const { error } = await supabase
        .from('permissions')
        .update({ is_deleted: true })
        .eq('id', permissionId);

      if (error) throw error;
      toast.success('Permission deleted successfully');
      fetchPermissions();
    } catch (error: any) {
      toast.error('Failed to delete permission: ' + error.message);
    }
  };

  const getPermissionActions = (permission: Permission) => {
    const actions = [];
    if (permission.can_view) actions.push('View');
    if (permission.can_add) actions.push('Add');
    if (permission.can_edit) actions.push('Edit');
    if (permission.can_delete) actions.push('Delete');
    return actions.join(', ');
  };

  if (permissionsLoading || loading) {
    return <div>Loading...</div>;
  }

  if (!canAccess('permissions_management')) {
    return (
      <div className="p-8 text-center text-red-600">
        <h2 className="text-xl font-bold">Access Denied</h2>
        <p>You do not have permission to manage permissions.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Permissions Management</h1>
          <p className="text-muted-foreground">Manage system permissions and role assignments</p>
        </div>

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            {hasPermission('permissions_management', 'add') && (
              <Button onClick={() => {
                setEditingPermission(null);
                resetForm();
              }}>
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Permission
              </Button>
            )}
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingPermission ? 'Edit Permission' : 'Add New Permission'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Permission Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., clients_view"
                  required
                />
              </div>
              <div>
                <Label htmlFor="module">Module</Label>
                {showCustomModule ? (
                  <div className="flex gap-2">
                    <Input
                      value={customModule}
                      onChange={(e) => setCustomModule(e.target.value)}
                      placeholder="Enter custom module name"
                      className="flex-1"
                    />
                    <Button type="button" onClick={handleCustomModuleSubmit} size="sm">
                      Add
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowCustomModule(false)} size="sm">
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <Select value={formData.module} onValueChange={handleModuleChange}>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Select module" />
                    </SelectTrigger>
                    <SelectContent className="bg-white border border-gray-200 shadow-lg">
                      {modules.map((module) => (
                        <SelectItem key={module} value={module} className="hover:bg-gray-100">
                          {module}
                        </SelectItem>
                      ))}
                      <SelectItem value="custom" className="hover:bg-gray-100 font-medium text-primary">
                        + Add Custom Module
                      </SelectItem>
                    </SelectContent>
                  </Select>
                )}
                {formData.module && !showCustomModule && (
                  <p className="text-sm text-gray-600 mt-1">Selected: {formData.module}</p>
                )}
              </div>
              <div>
                <Label>Actions</Label>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="can_view"
                      checked={formData.can_view}
                      onCheckedChange={(checked) => setFormData({ ...formData, can_view: !!checked })}
                    />
                    <Label htmlFor="can_view">View</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="can_add"
                      checked={formData.can_add}
                      onCheckedChange={(checked) => setFormData({ ...formData, can_add: !!checked })}
                    />
                    <Label htmlFor="can_add">Add</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="can_edit"
                      checked={formData.can_edit}
                      onCheckedChange={(checked) => setFormData({ ...formData, can_edit: !!checked })}
                    />
                    <Label htmlFor="can_edit">Edit</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="can_delete"
                      checked={formData.can_delete}
                      onCheckedChange={(checked) => setFormData({ ...formData, can_delete: !!checked })}
                    />
                    <Label htmlFor="can_delete">Delete</Label>
                  </div>
                </div>
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter permission description"
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingPermission ? 'Update' : 'Create'} Permission
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            System Permissions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Mobile Card View */}
          <div className="block lg:hidden space-y-4">
            {permissions.map((permission) => (
              <Card key={permission.id} className="p-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-start">
                    <h3 className="font-semibold">{permission.name}</h3>
                    <Badge variant="outline">{permission.module}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{permission.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {permission.can_view && <Badge variant="secondary" className="text-xs">View</Badge>}
                    {permission.can_add && <Badge variant="secondary" className="text-xs">Add</Badge>}
                    {permission.can_edit && <Badge variant="secondary" className="text-xs">Edit</Badge>}
                    {permission.can_delete && <Badge variant="secondary" className="text-xs">Delete</Badge>}
                  </div>
                  <div className="flex justify-end space-x-2 pt-2">
                    {hasPermission('permissions_management', 'edit') && (
                      <Button size="sm" variant="outline" onClick={() => handleEdit(permission)}>
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    )}
                    {hasPermission('permissions_management', 'delete') && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(permission.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden lg:block">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Permission Name</TableHead>
                  <TableHead>Module</TableHead>
                  <TableHead>Actions</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      Loading permissions...
                    </TableCell>
                  </TableRow>
                ) : permissions.length > 0 ? (
                  permissions.map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell className="font-medium">{permission.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{permission.module}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {permission.can_view && <Badge variant="secondary" className="text-xs">V</Badge>}
                          {permission.can_add && <Badge variant="secondary" className="text-xs">A</Badge>}
                          {permission.can_edit && <Badge variant="secondary" className="text-xs">E</Badge>}
                          {permission.can_delete && <Badge variant="secondary" className="text-xs">D</Badge>}
                        </div>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">{permission.description}</TableCell>
                      <TableCell>
                        <Badge variant={permission.is_active ? "default" : "secondary"}>
                          {permission.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="ghost" onClick={() => handleEdit(permission)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDelete(permission.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No permissions found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PermissionsManagement;