-- Trigger to automatically create user entry and assign default role on signup

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    client_role_id UUID;
BEGIN
    -- Get client role ID
    SELECT id INTO client_role_id FROM public.roles WHERE name = 'client';
    
    -- Insert into users table
    INSERT INTO public.users (id, email, role_id, created_at, updated_at)
    VALUES (NEW.id, NEW.email, client_role_id, now(), now());
    
    -- Insert into user_roles table
    INSERT INTO public.user_roles (user_id, role_id, assigned_by, created_at, updated_at)
    VALUES (NEW.id, client_role_id, NEW.id, now(), now());
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate trigger on auth.users table
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();