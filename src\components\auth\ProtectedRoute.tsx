import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { usePermissionContext } from '@/contexts/PermissionContext';
import { useAuth } from '@/contexts/AuthContext';

// You can create a more sophisticated loading spinner
const CenteredSpinner = () => (
  <div className="flex h-screen w-full items-center justify-center">
    <div className="h-16 w-16 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent"></div>
  </div>
);

interface ProtectedRouteProps {
  children: ReactNode;
  module: string;
  action?: 'view' | 'add' | 'edit' | 'delete'; // 'view' is default
}

const ProtectedRoute = ({ children, module, action = 'view' }: ProtectedRouteProps) => {
  const { user } = useAuth();
  const { hasPermission, loading, userRole } = usePermissionContext();

  // If permissions are still loading, show a spinner
  if (loading) {
    return <CenteredSpinner />;
  }

  // If the user is not logged in, redirect to the login page
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check for permission and redirect if not granted
  if (!hasPermission(module, action)) {
    // Redirect to the user's base dashboard or a dedicated 'unauthorized' page
    const fallbackPath = `/${userRole || 'login'}`;
    return <Navigate to={fallbackPath} replace />;
  }

  // If all checks pass, render the component
  return <>{children}</>;
};

export default ProtectedRoute;