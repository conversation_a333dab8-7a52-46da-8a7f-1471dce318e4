import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { 
  LayoutDashboard, 
  Users, 
  TrendingUp, 
  Settings, 
  Calculator,
  PieChart,
  Bell,
  FileText,
  UserCheck,
  Building2,
  Briefcase
} from "lucide-react";

interface DynamicSidebarProps {
  userRole: string;
}

const DynamicSidebar = ({ userRole }: DynamicSidebarProps) => {
  const location = useLocation();

  const getMenuItems = () => {
    switch (userRole) {
      case 'admin':
        return [
          { icon: LayoutDashboard, label: "Dashboard", href: "/admin" },
          { icon: Users, label: "Clients", href: "/admin/clients" },
          { icon: TrendingUp, label: "Investments", href: "/admin/investments" },
          { icon: FileText, label: "Schemes", href: "/admin/schemes" },
          { icon: Building2, label: "Brokers", href: "/admin/brokers" },
          { icon: Briefcase, label: "Broker Investments", href: "/admin/broker-investments" },
          { icon: User<PERSON><PERSON><PERSON>, label: "Team", href: "/admin/team" },
          { icon: Calculator, label: "Calculator", href: "/admin/calculator" },
          { icon: PieChart, label: "Portfolio", href: "/admin/portfolio" },
          { icon: FileText, label: "Blogs", href: "/admin/blogs" },
          { icon: Bell, label: "Alerts", href: "/admin/alerts" },
          { icon: Settings, label: "Settings", href: "/admin/settings" },
        ];
      case 'team_member':
        return [
          { icon: LayoutDashboard, label: "Dashboard", href: "/team" },
          { icon: Users, label: "Clients", href: "/team/clients" },
          { icon: TrendingUp, label: "Investments", href: "/team/investments" },
          { icon: Calculator, label: "Calculator", href: "/team/calculator" },
          { icon: PieChart, label: "Portfolio", href: "/team/portfolio" },
          { icon: Settings, label: "Settings", href: "/team/settings" },
        ];
      case 'client':
      default:
        return [
          { icon: LayoutDashboard, label: "Dashboard", href: "/client" },
          { icon: TrendingUp, label: "My Investments", href: "/client/investments" },
          { icon: PieChart, label: "Portfolio", href: "/client/portfolio" },
          { icon: Settings, label: "Settings", href: "/client/settings" },
        ];
    }
  };

  const menuItems = getMenuItems();

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 shadow-sm z-40">
      <div className="p-6">
        <h2 className="text-2xl font-bold text-primary">Care Capital</h2>
        <p className="text-sm text-muted-foreground capitalize">{userRole.replace('_', ' ')} Panel</p>
      </div>
      
      <nav className="px-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.href;
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors",
                isActive
                  ? "bg-primary text-white"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{item.label}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
};

export default DynamicSidebar;