import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

const Portfolio = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [portfolioStats, setPortfolioStats] = useState({
    totalValue: 0,
    activeInvestments: 0,
    averageReturns: 0,
    maturityThisMonth: 0
  });
  const [portfolioData, setPortfolioData] = useState<any[]>([]);
  const [performanceData, setPerformanceData] = useState<any[]>([]);
  const [topSchemes, setTopSchemes] = useState<any[]>([]);

  useEffect(() => {
    fetchPortfolioData();
  }, [user]);

  const fetchPortfolioData = async () => {
    try {
      setLoading(true);
      
      let investmentsQuery = supabase
        .from('investments')
        .select(`
          *,
          clients(first_name, last_name),
          schemes(name, monthly_interest_pct)
        `)
        .eq('is_deleted', false);

      // Role-based filtering
      if (user?.role === 'team_member') {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        const { data: teamMember } = await supabase
          .from('team_members')
          .select('id')
          .eq('user_id', currentUser?.id)
          .single();

        if (teamMember) {
          investmentsQuery = investmentsQuery.contains('team_members', `[{"id":"${teamMember.id}"}]`);
        }
      } else if (user?.role === 'client') {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        const { data: client } = await supabase
          .from('clients')
          .select('id')
          .eq('user_id', currentUser?.id)
          .single();

        if (client) {
          investmentsQuery = investmentsQuery.eq('client_id', client.id);
        }
      }

      const { data: investments, error } = await investmentsQuery;
      if (error) throw error;

      // Calculate portfolio stats
      const activeInvs = investments?.filter(inv => inv.status === 'active') || [];
      const totalValue = activeInvs.reduce((sum, inv) => sum + inv.amount, 0);
      const totalReturns = activeInvs.reduce((sum, inv) => sum + (inv.monthly_interest * 12), 0);
      const averageReturns = totalValue > 0 ? (totalReturns / totalValue) * 100 : 0;

      // Maturity this month
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const maturityThisMonth = activeInvs
        .filter(inv => {
          const endDate = new Date(inv.end_date);
          return endDate.getMonth() === currentMonth && endDate.getFullYear() === currentYear;
        })
        .reduce((sum, inv) => sum + inv.maturity_amount, 0);

      setPortfolioStats({
        totalValue,
        activeInvestments: activeInvs.length,
        averageReturns,
        maturityThisMonth
      });

      // Portfolio distribution by scheme
      const schemeDistribution = activeInvs.reduce((acc: any, inv) => {
        const schemeName = inv.schemes.name;
        if (!acc[schemeName]) {
          acc[schemeName] = { name: schemeName, value: 0, amount: 0 };
        }
        acc[schemeName].value += 1;
        acc[schemeName].amount += inv.amount;
        return acc;
      }, {});

      const portfolioChartData = Object.values(schemeDistribution).map((scheme: any, index) => ({
        ...scheme,
        color: ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'][index % 5]
      }));

      setPortfolioData(portfolioChartData);

      // Performance data (last 6 months)
      const performanceByMonth = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthName = date.toLocaleDateString('en', { month: 'short' });
        
        const monthInvestments = activeInvs.filter(inv => {
          const startDate = new Date(inv.start_date);
          return startDate <= date;
        });

        const monthlyReturns = monthInvestments.reduce((sum, inv) => sum + inv.monthly_interest, 0);
        const monthlyAmount = monthInvestments.reduce((sum, inv) => sum + inv.amount, 0);
        const returnsPct = monthlyAmount > 0 ? (monthlyReturns / monthlyAmount) * 100 : 0;

        performanceByMonth.push({
          month: monthName,
          returns: parseFloat(returnsPct.toFixed(1)),
          target: 10
        });
      }

      setPerformanceData(performanceByMonth);

      // Top performing schemes
      const schemePerformance = Object.values(schemeDistribution).map((scheme: any) => {
        const schemeInvestments = activeInvs.filter(inv => inv.schemes.name === scheme.name);
        const totalAmount = schemeInvestments.reduce((sum, inv) => sum + inv.amount, 0);
        const totalReturns = schemeInvestments.reduce((sum, inv) => sum + inv.monthly_interest * 12, 0);
        const returnsPct = totalAmount > 0 ? (totalReturns / totalAmount) * 100 : 0;

        return {
          scheme: scheme.name,
          returns: returnsPct.toFixed(1) + '%',
          investors: scheme.value,
          amount: '₹' + totalAmount.toLocaleString()
        };
      }).sort((a, b) => parseFloat(b.returns) - parseFloat(a.returns));

      setTopSchemes(schemePerformance);

    } catch (error: any) {
      toast.error('Failed to fetch portfolio data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-8 text-center">Loading portfolio...</div>;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Portfolio Overview</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{portfolioStats.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Active investments</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{portfolioStats.activeInvestments}</div>
            <p className="text-xs text-muted-foreground">Across all schemes</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{portfolioStats.averageReturns.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Annual percentage</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maturity This Month</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{portfolioStats.maturityThisMonth.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Expected maturity</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {portfolioData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={portfolioData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name} (${value})`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {portfolioData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-gray-500">
                No investment data available
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="returns" fill="#8B5CF6" name="Actual Returns %" />
                <Bar dataKey="target" fill="#06B6D4" name="Target Returns %" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Scheme Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topSchemes.length > 0 ? (
              topSchemes.map((scheme, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">{scheme.scheme}</h3>
                    <p className="text-sm text-muted-foreground">{scheme.investors} investments</p>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-green-600">{scheme.returns}</div>
                    <div className="text-sm text-muted-foreground">{scheme.amount}</div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-8">
                No scheme data available
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Portfolio;