
import React, { createContext, useContext, useState } from 'react';
import { supabase } from "@/integrations/supabase/client";

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'team_member' | 'client';
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, role?: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  // Initialize auth state from Supabase session
  React.useEffect(() => {
    const initializeAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        console.log('Found existing session for user:', session.user.id);
        
        // Get user data from database
        const { data: userData, error } = await supabase
          .from('users')
          .select(`
            *,
            roles(name)
          `)
          .eq('id', session.user.id)
          .single();

        if (!error && userData) {
          const user: User = {
            id: session.user.id,
            name: userData.first_name ? `${userData.first_name} ${userData.last_name || ''}`.trim() : session.user.email?.split('@')[0] || 'User',
            email: session.user.email || '',
            role: userData.roles?.name as 'admin' | 'team_member' | 'client' || 'client'
          };
          setUser(user);
        } else {
          console.log('User not found in database, clearing session');
          await supabase.auth.signOut();
        }
      } else {
        console.log('No active session found');
        // Clear any old localStorage data
        localStorage.removeItem('user');
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);
      
      if (event === 'SIGNED_OUT' || !session) {
        setUser(null);
        localStorage.removeItem('user');
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const login = async (email: string, password: string, role?: string) => {
    try {
      // Use Supabase authentication
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (authError) throw authError;
      if (!authData.user) throw new Error('No user returned from authentication');

      // Get user role from database
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          *,
          roles(name)
        `)
        .eq('id', authData.user.id)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);
        // Fallback to email-based role detection if user not in database
        let userRole: 'admin' | 'team_member' | 'client';
        if (role) {
          userRole = role as 'admin' | 'team_member' | 'client';
        } else if (email.includes('admin')) {
          userRole = 'admin';
        } else if (email.includes('team')) {
          userRole = 'team_member';
        } else {
          userRole = 'client';
        }
        
        const user: User = {
          id: authData.user.id,
          name: authData.user.user_metadata?.full_name || email.split('@')[0] || 'User',
          email: authData.user.email || email,
          role: userRole
        };
        
        setUser(user);
        return;
      }

      const user: User = {
        id: authData.user.id,
        name: userData.first_name ? `${userData.first_name} ${userData.last_name || ''}`.trim() : authData.user.email?.split('@')[0] || 'User',
        email: authData.user.email || email,
        role: userData.roles?.name as 'admin' | 'team_member' | 'client' || 'client'
      };
      
      setUser(user);
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = async () => {
    await supabase.auth.signOut();
    setUser(null);
    localStorage.removeItem('user');
    window.location.href = '/login';
  };



  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
