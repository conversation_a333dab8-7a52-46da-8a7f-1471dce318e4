-- Care Capital Investment System - Final Migration
-- Complete database schema with enums, tables, indexes, RLS policies, and functions

-- Create enums
CREATE TYPE public.alert_type AS ENUM ('maturity', 'payment_reminder', 'investment_due', 'system_notification');
CREATE TYPE public.notification_channel AS ENUM ('email', 'sms', 'whatsapp', 'push');
CREATE TYPE public.investment_status AS ENUM ('pending', 'active', 'completed', 'closed', 'suspended');
CREATE TYPE public.payment_status AS ENUM ('pending', 'paid', 'overdue', 'cancelled');
CREATE TYPE public.blog_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE public.transaction_type AS ENUM ('interest_payout', 'investment_allocation', 'withdrawal', 'commission_payment');

CREATE TABLE public.activity_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  table_name character varying NOT NULL,
  record_id uuid,
  action character varying NOT NULL,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT activity_logs_pkey PRIMARY KEY (id),
  CONSTRAINT activity_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.alerts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  investment_id uuid,
  user_id uuid,
  client_id uuid,
  alert_type alert_type NOT NULL,
  alert_date date NOT NULL,
  message text NOT NULL,
  status character varying DEFAULT 'pending'::character varying,
  channel notification_channel NOT NULL,
  sent_at timestamp with time zone,
  error_message text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT alerts_pkey PRIMARY KEY (id),
  CONSTRAINT alerts_investment_id_fkey FOREIGN KEY (investment_id) REFERENCES public.investments(id),
  CONSTRAINT alerts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT alerts_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT alerts_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT alerts_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT alerts_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.blogs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title character varying NOT NULL,
  slug character varying NOT NULL UNIQUE,
  author_id uuid NOT NULL,
  category character varying,
  tags text,
  cover_image_url text,
  excerpt text,
  content text NOT NULL,
  status blog_status DEFAULT 'draft'::blog_status,
  published_at timestamp with time zone,
  seo_title character varying,
  seo_description text,
  seo_keywords text,
  view_count integer DEFAULT 0,
  is_featured boolean DEFAULT false,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT blogs_pkey PRIMARY KEY (id),
  CONSTRAINT blogs_author_id_fkey FOREIGN KEY (author_id) REFERENCES auth.users(id),
  CONSTRAINT blogs_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT blogs_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.broker_investments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  broker_id uuid NOT NULL,
  amount_invested numeric NOT NULL,
  start_date date NOT NULL,
  end_date date,
  expected_return_pct numeric,
  status investment_status DEFAULT 'active'::investment_status,
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT broker_investments_pkey PRIMARY KEY (id),
  CONSTRAINT broker_investments_broker_id_fkey FOREIGN KEY (broker_id) REFERENCES public.brokers(id),
  CONSTRAINT broker_investments_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT broker_investments_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.broker_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  broker_investment_id uuid NOT NULL,
  transaction_type transaction_type NOT NULL,
  amount numeric NOT NULL,
  transaction_date timestamp with time zone DEFAULT now(),
  reference_number character varying,
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT broker_transactions_pkey PRIMARY KEY (id),
  CONSTRAINT broker_transactions_broker_investment_id_fkey FOREIGN KEY (broker_investment_id) REFERENCES public.broker_investments(id),
  CONSTRAINT broker_transactions_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT broker_transactions_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.brokers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  broker_code character varying NOT NULL UNIQUE,
  broker_name character varying NOT NULL,
  email character varying UNIQUE,
  phone character varying UNIQUE,
  address text,
  registration_number character varying,
  license_number character varying,
  commission_pct numeric DEFAULT 0,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT brokers_pkey PRIMARY KEY (id),
  CONSTRAINT brokers_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT brokers_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT brokers_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT brokers_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.charity_donations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  donor_name character varying,
  donor_email character varying,
  donor_phone character varying,
  amount numeric NOT NULL,
  donation_method character varying,
  transaction_reference character varying,
  cause character varying,
  is_anonymous boolean DEFAULT false,
  message text,
  receipt_sent boolean DEFAULT false,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT charity_donations_pkey PRIMARY KEY (id)
);
CREATE TABLE public.client_documents (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  client_id uuid NOT NULL,
  document_type character varying NOT NULL,
  document_url text NOT NULL,
  document_name character varying,
  verified boolean DEFAULT false,
  verified_by uuid,
  verified_at timestamp with time zone,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT client_documents_pkey PRIMARY KEY (id),
  CONSTRAINT client_documents_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT client_documents_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES auth.users(id),
  CONSTRAINT client_documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT client_documents_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.clients (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  client_code character varying UNIQUE,
  first_name character varying NOT NULL,
  last_name character varying,
  email character varying,
  mobile_number character varying NOT NULL UNIQUE,
  aadhar_number character varying UNIQUE,
  pan_card_number character varying UNIQUE,
  address text,
  city character varying,
  state character varying,
  pincode character varying,
  country character varying DEFAULT 'India'::character varying,
  client_photo_url text,
  account_number character varying,
  ifsc_code character varying,
  bank_name character varying,
  branch_name character varying,
  referred_by uuid,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT clients_pkey PRIMARY KEY (id),
  CONSTRAINT clients_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT clients_referred_by_fkey FOREIGN KEY (referred_by) REFERENCES public.team_members(id),
  CONSTRAINT clients_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT clients_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT clients_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.company_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  setting_key character varying NOT NULL UNIQUE,
  setting_value text,
  setting_type character varying DEFAULT 'text'::character varying,
  category character varying,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT company_settings_pkey PRIMARY KEY (id),
  CONSTRAINT company_settings_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT company_settings_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.contact_inquiries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  email character varying NOT NULL,
  phone character varying,
  subject character varying,
  message text NOT NULL,
  inquiry_type character varying,
  status character varying DEFAULT 'new'::character varying,
  assigned_to uuid,
  response text,
  responded_at timestamp with time zone,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT contact_inquiries_pkey PRIMARY KEY (id),
  CONSTRAINT contact_inquiries_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES auth.users(id)
);
CREATE TABLE public.investment_payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  investment_id uuid NOT NULL,
  month_number integer NOT NULL,
  due_date date NOT NULL,
  amount numeric NOT NULL,
  status payment_status DEFAULT 'pending'::payment_status,
  paid_at timestamp with time zone,
  paid_amount numeric,
  payment_method character varying,
  transaction_reference character varying,
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT investment_payments_pkey PRIMARY KEY (id),
  CONSTRAINT investment_payments_investment_id_fkey FOREIGN KEY (investment_id) REFERENCES public.investments(id),
  CONSTRAINT investment_payments_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT investment_payments_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.investments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  investment_code character varying UNIQUE,
  client_id uuid NOT NULL,
  scheme_id uuid NOT NULL,
  scheme_snapshot jsonb NOT NULL,
  amount numeric NOT NULL,
  start_date date NOT NULL,
  end_date date NOT NULL,
  monthly_interest numeric,
  total_expected_return numeric,
  team_members jsonb,
  total_commission_pct numeric DEFAULT 0,
  nominee_id uuid,
  status investment_status DEFAULT 'pending'::investment_status,
  maturity_amount numeric,
  approved_by uuid,
  approved_at timestamp with time zone,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT investments_pkey PRIMARY KEY (id),
  CONSTRAINT investments_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT investments_scheme_id_fkey FOREIGN KEY (scheme_id) REFERENCES public.schemes(id),
  CONSTRAINT investments_nominee_id_fkey FOREIGN KEY (nominee_id) REFERENCES public.nominees(id),
  CONSTRAINT investments_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES auth.users(id),
  CONSTRAINT investments_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT investments_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.nominees (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  client_id uuid NOT NULL,
  name character varying NOT NULL,
  relationship character varying,
  phone character varying,
  email character varying,
  address text,
  share_percentage numeric DEFAULT 100.00,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT nominees_pkey PRIMARY KEY (id),
  CONSTRAINT nominees_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT nominees_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT nominees_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  module character varying NOT NULL,
  can_view boolean DEFAULT false,
  can_add boolean DEFAULT false,
  can_edit boolean DEFAULT false,
  can_delete boolean DEFAULT false,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT permissions_pkey PRIMARY KEY (id)
);
CREATE TABLE public.role_permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  role_id uuid NOT NULL,
  permission_id uuid NOT NULL,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT role_permissions_pkey PRIMARY KEY (id),
  CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id),
  CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id)
);
CREATE TABLE public.roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT roles_pkey PRIMARY KEY (id)
);

CREATE TABLE public.permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  module character varying NOT NULL,
  can_view boolean DEFAULT false,
  can_add boolean DEFAULT false,
  can_edit boolean DEFAULT false,
  can_delete boolean DEFAULT false,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT permissions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.role_permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  role_id uuid NOT NULL,
  permission_id uuid NOT NULL,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT role_permissions_pkey PRIMARY KEY (id),
  CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id),
  CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id)
);
CREATE TABLE public.schemes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  min_investment numeric NOT NULL,
  max_investment numeric,
  tenure_months integer NOT NULL,
  monthly_interest_pct numeric NOT NULL,
  annual_interest_pct numeric,
  commission_pct numeric DEFAULT 0,
  lock_in_period integer DEFAULT 0,
  scheme_type character varying DEFAULT 'fixed'::character varying,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT schemes_pkey PRIMARY KEY (id),
  CONSTRAINT schemes_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT schemes_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.stock_training_inquiries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  email character varying NOT NULL,
  phone character varying NOT NULL,
  experience_level character varying,
  preferred_schedule character varying,
  message text,
  status character varying DEFAULT 'new'::character varying,
  follow_up_date date,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT stock_training_inquiries_pkey PRIMARY KEY (id)
);
CREATE TABLE public.team_members (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  refer_code character varying NOT NULL UNIQUE,
  first_name character varying NOT NULL,
  last_name character varying,
  email character varying UNIQUE,
  phone character varying UNIQUE,
  address text,
  city character varying,
  state character varying,
  pincode character varying,
  country character varying DEFAULT 'India'::character varying,
  account_number character varying,
  ifsc_code character varying,
  bank_name character varying,
  branch_name character varying,
  commission_pct numeric DEFAULT 0,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT team_members_pkey PRIMARY KEY (id),
  CONSTRAINT team_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT team_members_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT team_members_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT team_members_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  role_id uuid NOT NULL,
  assigned_at timestamp with time zone DEFAULT now(),
  assigned_by uuid,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT user_roles_pkey PRIMARY KEY (id),
  CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL,
  username character varying UNIQUE,
  role_id uuid,
  mobile character varying UNIQUE,
  first_name character varying,
  last_name character varying,
  email character varying,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT new_users_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id)
);

-- Create indexes for better performance
CREATE INDEX idx_users_role_id ON public.users(role_id);
CREATE INDEX idx_users_mobile ON public.users(mobile);
CREATE INDEX idx_clients_mobile ON public.clients(mobile_number);
CREATE INDEX idx_clients_referred_by ON public.clients(referred_by);
CREATE INDEX idx_clients_user_id ON public.clients(user_id);
CREATE INDEX idx_investments_client_id ON public.investments(client_id);
CREATE INDEX idx_investments_scheme_id ON public.investments(scheme_id);
CREATE INDEX idx_investments_status ON public.investments(status);
CREATE INDEX idx_investments_approved_by ON public.investments(approved_by);
CREATE INDEX idx_investments_approved_at ON public.investments(approved_at);
CREATE INDEX idx_investment_payments_investment_id ON public.investment_payments(investment_id);
CREATE INDEX idx_investment_payments_status ON public.investment_payments(status);
CREATE INDEX idx_investment_payments_due_date ON public.investment_payments(due_date);
CREATE INDEX idx_alerts_alert_date ON public.alerts(alert_date);
CREATE INDEX idx_alerts_status ON public.alerts(status);
CREATE INDEX idx_alerts_user_id ON public.alerts(user_id);
CREATE INDEX idx_alerts_client_id ON public.alerts(client_id);
CREATE INDEX idx_blogs_status ON public.blogs(status);
CREATE INDEX idx_blogs_published_at ON public.blogs(published_at);
CREATE INDEX idx_blogs_author_id ON public.blogs(author_id);
CREATE INDEX idx_activity_logs_user_id ON public.activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON public.activity_logs(created_at);
CREATE INDEX idx_team_members_refer_code ON public.team_members(refer_code);
CREATE INDEX idx_brokers_broker_code ON public.brokers(broker_code);
CREATE INDEX idx_broker_investments_broker_id ON public.broker_investments(broker_id);
CREATE INDEX idx_broker_transactions_broker_investment_id ON public.broker_transactions(broker_investment_id);
CREATE INDEX idx_nominees_client_id ON public.nominees(client_id);
CREATE INDEX idx_client_documents_client_id ON public.client_documents(client_id);
CREATE INDEX idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON public.role_permissions(permission_id);
CREATE INDEX idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON public.user_roles(role_id);
CREATE INDEX idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON public.role_permissions(permission_id);

-- Enable Row Level Security on all tables
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.broker_investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.broker_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.brokers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.charity_donations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investment_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.nominees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schemes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_training_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Helper functions
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT r.name FROM public.users u 
    JOIN public.roles r ON u.role_id = r.id 
    WHERE u.id = auth.uid() AND u.is_active = true AND u.is_deleted = false;
$$;

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT public.get_current_user_role() = 'admin';
$$;

CREATE OR REPLACE FUNCTION public.is_team_member()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT public.get_current_user_role() IN ('admin', 'team_member');
$$;

CREATE OR REPLACE FUNCTION public.has_permission(module_name TEXT, permission_type TEXT)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.user_roles ur ON u.id = ur.user_id
        JOIN public.role_permissions rp ON ur.role_id = rp.role_id
        JOIN public.permissions p ON rp.permission_id = p.id
        WHERE u.id = auth.uid() 
        AND u.is_active = true 
        AND u.is_deleted = false
        AND ur.is_active = true 
        AND ur.is_deleted = false
        AND rp.is_active = true 
        AND rp.is_deleted = false
        AND p.is_active = true 
        AND p.is_deleted = false
        AND p.module = module_name
        AND (
            (permission_type = 'view' AND p.can_view = true) OR
            (permission_type = 'add' AND p.can_add = true) OR
            (permission_type = 'edit' AND p.can_edit = true) OR
            (permission_type = 'delete' AND p.can_delete = true)
        )
    );
$$;

-- Trigger function for automatic timestamp updates
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Add updated_at triggers to all relevant tables
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON public.roles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_permissions_updated_at
    BEFORE UPDATE ON public.permissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at
    BEFORE UPDATE ON public.role_permissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_permissions_updated_at
    BEFORE UPDATE ON public.permissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at
    BEFORE UPDATE ON public.role_permissions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at
    BEFORE UPDATE ON public.user_roles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_team_members_updated_at
    BEFORE UPDATE ON public.team_members
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_clients_updated_at
    BEFORE UPDATE ON public.clients
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_client_documents_updated_at
    BEFORE UPDATE ON public.client_documents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_nominees_updated_at
    BEFORE UPDATE ON public.nominees
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_schemes_updated_at
    BEFORE UPDATE ON public.schemes
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_investments_updated_at
    BEFORE UPDATE ON public.investments
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_investment_payments_updated_at
    BEFORE UPDATE ON public.investment_payments
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_brokers_updated_at
    BEFORE UPDATE ON public.brokers
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_broker_investments_updated_at
    BEFORE UPDATE ON public.broker_investments
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_broker_transactions_updated_at
    BEFORE UPDATE ON public.broker_transactions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_company_settings_updated_at
    BEFORE UPDATE ON public.company_settings
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_alerts_updated_at
    BEFORE UPDATE ON public.alerts
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_blogs_updated_at
    BEFORE UPDATE ON public.blogs
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default roles
INSERT INTO public.roles (name, description) VALUES
('admin', 'System Administrator with full access'),
('team_member', 'Team member with limited access'),
('client', 'Client user with restricted access')
ON CONFLICT (name) DO NOTHING;

-- Insert default permissions
INSERT INTO public.permissions (name, module, can_view, can_add, can_edit, can_delete, description) VALUES
('clients_view', 'clients', true, false, false, false, 'View clients'),
('clients_add', 'clients', true, true, false, false, 'Add new clients'),
('clients_edit', 'clients', true, true, true, false, 'Edit client information'),
('clients_delete', 'clients', true, true, true, true, 'Delete clients'),
('investments_view', 'investments', true, false, false, false, 'View investments'),
('investments_add', 'investments', true, true, false, false, 'Add new investments'),
('investments_edit', 'investments', true, true, true, false, 'Edit investments'),
('investments_delete', 'investments', true, true, true, true, 'Delete investments'),
('schemes_view', 'schemes', true, false, false, false, 'View schemes'),
('schemes_add', 'schemes', true, true, false, false, 'Add new schemes'),
('schemes_edit', 'schemes', true, true, true, false, 'Edit schemes'),
('schemes_delete', 'schemes', true, true, true, true, 'Delete schemes'),
('team_view', 'team', true, false, false, false, 'View team members'),
('team_add', 'team', true, true, false, false, 'Add team members'),
('team_edit', 'team', true, true, true, false, 'Edit team members'),
('team_delete', 'team', true, true, true, true, 'Delete team members'),
('brokers_view', 'brokers', true, false, false, false, 'View brokers'),
('brokers_add', 'brokers', true, true, false, false, 'Add brokers'),
('brokers_edit', 'brokers', true, true, true, false, 'Edit brokers'),
('brokers_delete', 'brokers', true, true, true, true, 'Delete brokers'),
('analytics_view', 'analytics', true, false, false, false, 'View analytics and reports')
ON CONFLICT (name) DO NOTHING;

-- Assign permissions to roles
DO $$
DECLARE
    admin_role_id UUID;
    team_role_id UUID;
    client_role_id UUID;
BEGIN
    SELECT id INTO admin_role_id FROM public.roles WHERE name = 'admin';
    SELECT id INTO team_role_id FROM public.roles WHERE name = 'team_member';
    SELECT id INTO client_role_id FROM public.roles WHERE name = 'client';

    -- Admin gets all permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT admin_role_id, id FROM public.permissions
    ON CONFLICT (role_id, permission_id) DO NOTHING;

    -- Team member gets limited permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT team_role_id, id FROM public.permissions 
    WHERE name IN ('clients_view', 'clients_add', 'clients_edit', 'investments_view', 'investments_add', 'investments_edit', 'analytics_view')
    ON CONFLICT (role_id, permission_id) DO NOTHING;

    -- Client gets very limited permissions
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT client_role_id, id FROM public.permissions 
    WHERE name IN ('investments_view', 'analytics_view')
    ON CONFLICT (role_id, permission_id) DO NOTHING;
END $$;

-- Trigger to automatically create user entry and assign default role on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    client_role_id UUID;
BEGIN
    -- Get client role ID
    SELECT id INTO client_role_id FROM public.roles WHERE name = 'client';
    
    -- Insert into users table
    INSERT INTO public.users (id, email, role_id, created_at, updated_at)
    VALUES (NEW.id, NEW.email, client_role_id, now(), now());
    
    -- Insert into user_roles table
    INSERT INTO public.user_roles (user_id, role_id, assigned_by, created_at, updated_at)
    VALUES (NEW.id, client_role_id, NEW.id, now(), now());
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on auth.users table
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create profiles view for Supabase compatibility
CREATE OR REPLACE VIEW public.profiles AS
SELECT 
    id,
    email,
    first_name,
    last_name,
    mobile as phone,
    created_at,
    updated_at
FROM public.users;

-- RLS policies for main tables
CREATE POLICY "Enable read for authenticated users" ON public.users FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.team_members FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.team_members FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.team_members FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.clients FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.clients FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.clients FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.brokers FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.brokers FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.brokers FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.broker_investments FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.broker_investments FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.broker_investments FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.broker_transactions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.broker_transactions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.broker_transactions FOR UPDATE USING (auth.role() = 'authenticated');

-- Additional tables for broker investment management
CREATE TABLE public.company_funds (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  total_available numeric DEFAULT 0,
  total_invested numeric DEFAULT 0,
  total_commission_paid numeric DEFAULT 0,
  total_profit numeric DEFAULT 0,
  last_updated timestamp with time zone DEFAULT now(),
  updated_by uuid,
  CONSTRAINT company_funds_pkey PRIMARY KEY (id),
  CONSTRAINT company_funds_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.fund_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  transaction_type character varying NOT NULL, -- 'deposit', 'investment', 'commission', 'profit', 'withdrawal'
  amount numeric NOT NULL,
  description text,
  broker_investment_id uuid,
  reference_number character varying,
  balance_after numeric,
  created_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  CONSTRAINT fund_transactions_pkey PRIMARY KEY (id),
  CONSTRAINT fund_transactions_broker_investment_id_fkey FOREIGN KEY (broker_investment_id) REFERENCES public.broker_investments(id),
  CONSTRAINT fund_transactions_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);

-- Add new columns to broker_investments for better tracking
ALTER TABLE public.broker_investments ADD COLUMN IF NOT EXISTS maturity_amount numeric;
ALTER TABLE public.broker_investments ADD COLUMN IF NOT EXISTS actual_return numeric;
ALTER TABLE public.broker_investments ADD COLUMN IF NOT EXISTS profit_amount numeric;
ALTER TABLE public.broker_investments ADD COLUMN IF NOT EXISTS investment_code character varying UNIQUE;

-- Enable RLS on new tables
ALTER TABLE public.company_funds ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fund_transactions ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for new tables
CREATE POLICY "Enable read for authenticated users" ON public.company_funds FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.company_funds FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.company_funds FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.fund_transactions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.fund_transactions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.fund_transactions FOR UPDATE USING (auth.role() = 'authenticated');

-- Insert initial company funds record
INSERT INTO public.company_funds (total_available, total_invested, total_commission_paid, total_profit) 
VALUES (0, 0, 0, 0) ON CONFLICT DO NOTHING;

-- Create indexes for new tables
CREATE INDEX idx_fund_transactions_type ON public.fund_transactions(transaction_type);
CREATE INDEX idx_fund_transactions_created_at ON public.fund_transactions(created_at);
CREATE INDEX idx_broker_investments_code ON public.broker_investments(investment_code);

-- Company funds and investment approval are handled in separate migration files

-- Create RPC function to get user role without RLS issues
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT r.name 
  FROM users u
  JOIN roles r ON u.role_id = r.id
  WHERE u.id = user_id
  AND u.is_active = true 
  AND u.is_deleted = false
  LIMIT 1;
$$;