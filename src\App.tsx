import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { PermissionProvider } from "@/contexts/PermissionContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
// Dashboard Layout
import AdminLayout from "@/components/layouts/AdminLayout";

// Admin Routes
import AdminDashboard from "@/pages/admin/AdminDashboard";
import ClientsManagement from "@/pages/admin/ClientsManagement";
import AddClient from "@/pages/admin/AddClient";
import ViewClient from "@/pages/admin/ViewClient";
import InvestmentManagement from "@/pages/admin/InvestmentManagement";
import SchemesManagement from "@/pages/admin/SchemesManagement";
import AddScheme from "@/pages/admin/AddScheme";
import ViewScheme from "@/pages/admin/ViewScheme";
import AddInvestment from "@/pages/admin/AddInvestment";
import ViewInvestment from "@/pages/admin/ViewInvestment";
import BrokerInvestmentManagement from "@/pages/admin/BrokerInvestmentManagement";
import BrokersManagement from "@/pages/admin/BrokersManagement";
import TeamManagement from "@/pages/admin/TeamManagement";
import AddTeamMember from "@/pages/admin/AddTeamMember";
import ViewTeamMember from "@/pages/admin/ViewTeamMember";
import Calculator from "@/pages/admin/Calculator";
import Portfolio from "@/pages/admin/Portfolio";
import BlogManagement from "@/pages/admin/BlogManagement";
import AlertsNotifications from "@/pages/admin/AlertsNotifications";
import RolesManagement from "@/pages/admin/RolesManagement";
import PermissionsManagement from "@/pages/admin/PermissionsManagement";
import AdminSettings from "@/pages/admin/AdminSettings";
import CompanySettings from "@/pages/admin/CompanySettings";

// Team Routes
import TeamDashboard from "@/pages/team/TeamDashboard";
import TeamClients from "@/pages/team/TeamClients";
import TeamInvestments from "@/pages/team/TeamInvestments";
import TeamCalculator from "@/pages/team/TeamCalculator";
import TeamPortfolio from "@/pages/team/TeamPortfolio";
import TeamSettings from "@/pages/team/TeamSettings";

// Client Routes
import ClientDashboard from "@/pages/client/ClientDashboard";
import ClientInvestments from "@/pages/client/ClientInvestments";
import ClientPortfolio from "@/pages/client/ClientPortfolio";
import ClientSettings from "@/pages/client/ClientSettings";

// Marketing Website Routes
import MarketingLayout from "@/components/layouts/MarketingLayout";
import HomePage from "@/pages/marketing/HomePage";
import StockTraining from "@/pages/marketing/StockTraining";
import Charity from "@/pages/marketing/Charity";
import AboutUs from "@/pages/marketing/AboutUs";
import Contact from "@/pages/marketing/Contact";
import FAQs from "@/pages/marketing/FAQs";
import Blogs from "@/pages/marketing/Blogs";
import BlogDetail from "@/pages/marketing/BlogDetail";

// Auth Pages
import LoginPage from "@/pages/auth/LoginPage";
import ForgotPasswordPage from "@/pages/auth/ForgotPasswordPage";
import ResetPasswordPage from "@/pages/auth/ResetPasswordPage";
import AuthCallback from "@/pages/auth/AuthCallback";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <PermissionProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              {/* --- AUTH ROUTES (Public) --- */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/forgot-password" element={<ForgotPasswordPage />} />
              <Route path="/reset-password" element={<ResetPasswordPage />} />
              <Route path="/auth/callback" element={<AuthCallback />} />

              {/* --- ADMIN DASHBOARD ROUTES (Protected) --- */}
              <Route path="/admin" element={<AdminLayout />}>
                <Route index element={<ProtectedRoute module="dashboard_admin"><AdminDashboard /></ProtectedRoute>} />
                <Route path="clients" element={<ProtectedRoute module="clients"><ClientsManagement /></ProtectedRoute>} />
                <Route path="clients/add" element={<ProtectedRoute module="clients" action="add"><AddClient /></ProtectedRoute>} />
                <Route path="clients/view/:id" element={<ProtectedRoute module="clients"><ViewClient /></ProtectedRoute>} />
                <Route path="clients/edit/:id" element={<ProtectedRoute module="clients" action="edit"><AddClient /></ProtectedRoute>} />
                <Route path="investments" element={<ProtectedRoute module="investments"><InvestmentManagement /></ProtectedRoute>} />
                <Route path="investments/add" element={<ProtectedRoute module="investments" action="add"><AddInvestment /></ProtectedRoute>} />
                <Route path="investments/edit/:id" element={<ProtectedRoute module="investments" action="edit"><AddInvestment /></ProtectedRoute>} />
                <Route path="investments/view/:id" element={<ProtectedRoute module="investments"><ViewInvestment /></ProtectedRoute>} />
                <Route path="schemes" element={<ProtectedRoute module="schemes"><SchemesManagement /></ProtectedRoute>} />
                <Route path="schemes/add" element={<ProtectedRoute module="schemes" action="add"><AddScheme /></ProtectedRoute>} />
                <Route path="schemes/edit/:id" element={<ProtectedRoute module="schemes" action="edit"><AddScheme /></ProtectedRoute>} />
                <Route path="schemes/view/:id" element={<ProtectedRoute module="schemes"><ViewScheme /></ProtectedRoute>} />
                <Route path="broker-investments" element={<ProtectedRoute module="broker_investments"><BrokerInvestmentManagement /></ProtectedRoute>} />
                <Route path="brokers" element={<ProtectedRoute module="brokers"><BrokersManagement /></ProtectedRoute>} />
                <Route path="team" element={<ProtectedRoute module="team"><TeamManagement /></ProtectedRoute>} />
                <Route path="team/add" element={<ProtectedRoute module="team" action="add"><AddTeamMember /></ProtectedRoute>} />
                <Route path="team/view/:id" element={<ProtectedRoute module="team"><ViewTeamMember /></ProtectedRoute>} />
                <Route path="team/edit/:id" element={<ProtectedRoute module="team" action="edit"><AddTeamMember /></ProtectedRoute>} />
                <Route path="calculator" element={<ProtectedRoute module="calculator"><Calculator /></ProtectedRoute>} />
                <Route path="portfolio" element={<ProtectedRoute module="portfolio"><Portfolio /></ProtectedRoute>} />
                <Route path="blogs" element={<ProtectedRoute module="blogs"><BlogManagement /></ProtectedRoute>} />
                <Route path="alerts" element={<ProtectedRoute module="alerts"><AlertsNotifications /></ProtectedRoute>} />
                <Route path="roles" element={<ProtectedRoute module="roles"><RolesManagement /></ProtectedRoute>} />
                <Route path="permissions" element={<ProtectedRoute module="permissions_management"><PermissionsManagement /></ProtectedRoute>} />
                <Route path="settings" element={<ProtectedRoute module="settings"><AdminSettings /></ProtectedRoute>} />
                <Route path="company-settings" element={<ProtectedRoute module="settings"><CompanySettings /></ProtectedRoute>} />
              </Route>

              {/* --- TEAM DASHBOARD ROUTES (Protected) --- */}
              <Route path="/team_member" element={<AdminLayout />}>
                <Route index element={<ProtectedRoute module="investments"><TeamDashboard /></ProtectedRoute>} />
                <Route path="clients" element={<ProtectedRoute module="clients"><TeamClients /></ProtectedRoute>} />
                <Route path="investments" element={<ProtectedRoute module="investments"><TeamInvestments /></ProtectedRoute>} />
                <Route path="calculator" element={<ProtectedRoute module="calculator"><TeamCalculator /></ProtectedRoute>} />
                <Route path="portfolio" element={<ProtectedRoute module="portfolio"><TeamPortfolio /></ProtectedRoute>} />
                <Route path="settings" element={<ProtectedRoute module="settings"><TeamSettings /></ProtectedRoute>} />
              </Route>

              {/* --- CLIENT DASHBOARD ROUTES (Protected) --- */}
              <Route path="/client" element={<AdminLayout />}>
                <Route index element={<ProtectedRoute module="dashboard"><ClientDashboard /></ProtectedRoute>} />
                <Route path="investments" element={<ProtectedRoute module="investments"><ClientInvestments /></ProtectedRoute>} />
                <Route path="portfolio" element={<ProtectedRoute module="portfolio"><ClientPortfolio /></ProtectedRoute>} />
                <Route path="settings" element={<ProtectedRoute module="settings"><ClientSettings /></ProtectedRoute>} />
              </Route>

              {/* --- MARKETING WEBSITE ROUTES (Public) --- */}
              <Route path="/" element={<MarketingLayout />}>
                <Route index element={<HomePage />} />
                <Route path="stock-training" element={<StockTraining />} />
                <Route path="charity" element={<Charity />} />
                <Route path="about" element={<AboutUs />} />
                <Route path="contact" element={<Contact />} />
                <Route path="faqs" element={<FAQs />} />
                <Route path="blogs" element={<Blogs />} />
                <Route path="blog/:slug" element={<BlogDetail />} />
              </Route>
            </Routes>
          </BrowserRouter>
        </PermissionProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;