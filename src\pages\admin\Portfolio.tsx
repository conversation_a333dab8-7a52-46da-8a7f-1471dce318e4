import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line, AreaChart, Area } from "recharts";
import { TrendingUp, TrendingDown, DollarSign, Target, Calendar, Users, BarChart3, <PERSON><PERSON><PERSON> as PieChartIcon } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermission";
import { toast } from "sonner";

interface Investment {
  id: string;
  client_id: string;
  scheme_id: string;
  amount: number;
  start_date: string;
  maturity_date: string;
  expected_return: number;
  status: string;
  clients: { first_name: string; last_name: string; email: string };
  schemes: { name: string; monthly_interest_pct: number; annual_interest_pct: number; tenure_months: number };
}

interface PortfolioStats {
  totalValue: number;
  totalInvestments: number;
  averageReturns: number;
  maturityThisMonth: number;
  totalClients: number;
  activeSchemes: number;
  totalEarnings: number;
}

const Portfolio = () => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [stats, setStats] = useState<PortfolioStats>({
    totalValue: 0,
    totalInvestments: 0,
    averageReturns: 0,
    maturityThisMonth: 0,
    totalClients: 0,
    activeSchemes: 0,
    totalEarnings: 0
  });
  const [loading, setLoading] = useState(true);
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');
  const { user } = useAuth();
  const { canAccess, hasPermission, userRole } = usePermissions();

  useEffect(() => {
    fetchPortfolioData();
  }, [user]);

  const fetchPortfolioData = async () => {
    try {
      let query = supabase
        .from('investments')
        .select(`
          *,
          clients(first_name, last_name, email),
          schemes(name, monthly_interest_pct, annual_interest_pct, tenure_months)
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      const { data, error } = await query;
      if (error) throw error;

      let filteredData = data || [];

      // Role-based filtering
      if (userRole === 'team_member') {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        
        if (currentUser?.id && filteredData.length > 0) {
          const validInvestmentIds = [];
          let currentTeamMemberId = null;
          
          // Get current user's team member ID
          const { data: currentTeamMember } = await supabase
            .from('team_members')
            .select('id')
            .eq('user_id', currentUser.id)
            .single();
          
          if (currentTeamMember) {
            currentTeamMemberId = currentTeamMember.id;
          }
          
          for (const investment of filteredData) {
            if (investment.team_members) {
              try {
                const teamMembers = JSON.parse(investment.team_members);
                const teamMemberIds = teamMembers.map((tm: any) => tm.id);
                
                if (teamMemberIds.length > 0) {
                  const { data: matchingMembers } = await supabase
                    .from('team_members')
                    .select('id, user_id')
                    .in('id', teamMemberIds)
                    .eq('user_id', currentUser.id);
                  
                  if (matchingMembers && matchingMembers.length > 0) {
                    validInvestmentIds.push(investment.id);
                    // Store current team member ID for earnings calculation
                    investment.currentTeamMemberId = currentTeamMemberId;
                  }
                }
              } catch (e) {
                console.error('Error parsing team_members JSON:', e);
              }
            }
          }
          
          filteredData = filteredData.filter(inv => validInvestmentIds.includes(inv.id));
        } else {
          filteredData = [];
        }
      } else if (userRole === 'client') {
        // Client sees only their own investments
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        if (currentUser?.id) {
          // Get client record for current user
          const { data: clientData } = await supabase
            .from('clients')
            .select('id')
            .eq('user_id', currentUser.id)
            .single();
          
          if (clientData) {
            filteredData = filteredData.filter(inv => inv.client_id === clientData.id);
          } else {
            filteredData = [];
          }
        } else {
          filteredData = [];
        }
      }
      // Admin sees all investments

      setInvestments(filteredData);
      calculateStats(filteredData);
    } catch (error: any) {
      toast.error('Failed to fetch portfolio data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (investmentData: Investment[]) => {
    const totalValue = investmentData.reduce((sum, inv) => sum + inv.amount, 0);
    const totalInvestments = investmentData.length;
    const averageReturns = investmentData.reduce((sum, inv) => sum + (inv.schemes?.annual_interest_pct || 0), 0) / totalInvestments || 0;
    
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const maturityThisMonth = investmentData
      .filter(inv => {
        const maturityDate = new Date(inv.maturity_date);
        return maturityDate.getMonth() === currentMonth && maturityDate.getFullYear() === currentYear;
      })
      .reduce((sum, inv) => sum + inv.amount, 0);

    const uniqueClients = new Set(investmentData.map(inv => inv.client_id)).size;
    const uniqueSchemes = new Set(investmentData.map(inv => inv.scheme_id)).size;

    // Calculate total earnings for current team member only
    let totalEarnings = 0;
    if (userRole === 'team_member') {
      totalEarnings = investmentData.reduce((sum, inv) => {
        if (inv.team_members && inv.currentTeamMemberId) {
          try {
            const teamMembers = JSON.parse(inv.team_members);
            // Find current team member's commission only
            const currentMemberEarning = teamMembers.find((tm: any) => tm.id === inv.currentTeamMemberId);
            return sum + (currentMemberEarning?.commission_amount || 0);
          } catch (e) {
            return sum;
          }
        }
        return sum;
      }, 0);
    }

    setStats({
      totalValue,
      totalInvestments,
      averageReturns,
      maturityThisMonth,
      totalClients: uniqueClients,
      activeSchemes: uniqueSchemes,
      totalEarnings
    });
  };

  const getSchemeDistribution = () => {
    const schemeMap = new Map();
    investments.forEach(inv => {
      const schemeName = inv.schemes?.name || 'Unknown';
      if (schemeMap.has(schemeName)) {
        schemeMap.set(schemeName, schemeMap.get(schemeName) + inv.amount);
      } else {
        schemeMap.set(schemeName, inv.amount);
      }
    });

    const colors = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B'];
    return Array.from(schemeMap.entries()).map(([name, value], index) => ({
      name,
      value,
      color: colors[index % colors.length]
    }));
  };

  const getMonthlyPerformance = () => {
    const monthlyData = Array.from({ length: 12 }, (_, i) => {
      const month = new Date(2024, i, 1).toLocaleDateString('en', { month: 'short' });
      const monthInvestments = investments.filter(inv => {
        const startDate = new Date(inv.start_date);
        return startDate.getMonth() === i;
      });
      
      const totalAmount = monthInvestments.reduce((sum, inv) => sum + inv.amount, 0);
      const avgReturn = monthInvestments.reduce((sum, inv) => sum + (inv.schemes?.annual_interest_pct || 0), 0) / monthInvestments.length || 0;
      
      return {
        month,
        amount: totalAmount / 100000, // Convert to lakhs
        returns: avgReturn,
        target: 12
      };
    });
    return monthlyData;
  };

  const getTopPerformingSchemes = () => {
    const schemePerformance = new Map();
    investments.forEach(inv => {
      const schemeName = inv.schemes?.name || 'Unknown';
      const returnRate = inv.schemes?.annual_interest_pct || 0;
      
      if (schemePerformance.has(schemeName)) {
        const existing = schemePerformance.get(schemeName);
        schemePerformance.set(schemeName, {
          ...existing,
          totalAmount: existing.totalAmount + inv.amount,
          investorCount: existing.investorCount + 1,
          avgReturn: (existing.avgReturn + returnRate) / 2
        });
      } else {
        schemePerformance.set(schemeName, {
          totalAmount: inv.amount,
          investorCount: 1,
          avgReturn: returnRate
        });
      }
    });

    return Array.from(schemePerformance.entries())
      .map(([name, data]) => ({ scheme: name, ...data }))
      .sort((a, b) => b.avgReturn - a.avgReturn)
      .slice(0, 5);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (!canAccess('portfolio')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view portfolio.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const schemeDistribution = getSchemeDistribution();
  const monthlyPerformance = getMonthlyPerformance();
  const topSchemes = getTopPerformingSchemes();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Portfolio Overview</h1>
          <p className="text-gray-600 mt-1">
            {user?.user_metadata?.role === 'admin' ? 'Complete portfolio analytics' :
             user?.user_metadata?.role === 'team_member' ? 'Your managed investments' :
             'Your investment portfolio'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={chartType === 'pie' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setChartType('pie')}
          >
            <PieChartIcon className="h-4 w-4 mr-2" />
            Pie View
          </Button>
          <Button
            variant={chartType === 'bar' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setChartType('bar')}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Bar View
          </Button>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +12.5% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Investments</CardTitle>
            <Target className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalInvestments}</div>
            <p className="text-xs text-muted-foreground">
              Across {stats.activeSchemes} schemes
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Returns</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageReturns.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Annual percentage</p>
          </CardContent>
        </Card>
        
        {userRole === 'team_member' ? (
          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalEarnings)}</div>
              <p className="text-xs text-muted-foreground">Commission earned</p>
            </CardContent>
          </Card>
        ) : (
          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Maturity This Month</CardTitle>
              <Calendar className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.maturityThisMonth)}</div>
              <p className="text-xs text-muted-foreground">
                {userRole === 'admin' && `${stats.totalClients} clients`}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Portfolio Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {chartType === 'pie' ? (
                <PieChart>
                  <Pie
                    data={schemeDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {schemeDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                </PieChart>
              ) : (
                <BarChart data={schemeDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis tickFormatter={(value) => `₹${(value / 100000).toFixed(0)}L`} />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Bar dataKey="value" fill="#8B5CF6" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Monthly Investment Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={monthlyPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" tickFormatter={(value) => `₹${value}L`} />
                <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `${value}%`} />
                <Tooltip 
                  formatter={(value, name) => {
                    if (name === 'amount') return [`₹${value}L`, 'Investment Amount'];
                    return [`${value}%`, name === 'returns' ? 'Returns' : 'Target'];
                  }}
                />
                <Legend />
                <Area yAxisId="left" type="monotone" dataKey="amount" stackId="1" stroke="#8B5CF6" fill="#8B5CF6" fillOpacity={0.6} name="Investment (₹L)" />
                <Line yAxisId="right" type="monotone" dataKey="returns" stroke="#10B981" strokeWidth={2} name="Returns %" />
                <Line yAxisId="right" type="monotone" dataKey="target" stroke="#EF4444" strokeWidth={2} strokeDasharray="5 5" name="Target %" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Schemes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Top Performing Schemes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topSchemes.map((scheme, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{scheme.scheme}</h3>
                    <p className="text-sm text-muted-foreground flex items-center gap-2">
                      <Users className="h-3 w-3" />
                      {scheme.investorCount} investors
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      {scheme.avgReturn.toFixed(1)}% Returns
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {formatCurrency(scheme.totalAmount)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Investments Table */}
      {hasPermission('investments', 'view') && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Client</th>
                    <th className="text-left p-2">Scheme</th>
                    <th className="text-left p-2">Amount</th>
                    <th className="text-left p-2">Expected Return</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Maturity</th>
                  </tr>
                </thead>
                <tbody>
                  {investments.slice(0, 10).map((investment) => (
                    <tr key={investment.id} className="border-b hover:bg-gray-50">
                      <td className="p-2">
                        <div>
                          <div className="font-medium">
                            {`${investment.clients?.first_name} ${investment.clients?.last_name || ''}`.trim()}
                          </div>
                          <div className="text-sm text-gray-500">{investment.clients?.email}</div>
                        </div>
                      </td>
                      <td className="p-2">{investment.schemes?.name}</td>
                      <td className="p-2 font-medium">{formatCurrency(investment.amount)}</td>
                      <td className="p-2">
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          {investment.schemes?.annual_interest_pct}%
                        </Badge>
                      </td>
                      <td className="p-2">
                        <Badge 
                          variant={investment.status === 'active' ? 'default' : 'secondary'}
                        >
                          {investment.status}
                        </Badge>
                      </td>
                      <td className="p-2 text-sm text-gray-600">
                        {new Date(investment.maturity_date).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Portfolio;
