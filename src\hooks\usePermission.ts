import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/integrations/supabase/client'

interface Permission {
  id: string
  name: string
  module: string
  can_view: boolean
  can_add: boolean
  can_edit: boolean
  can_delete: boolean
}

export const usePermissions = () => {
  const { user } = useAuth()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user && user.role) {
      fetchPermissions()
    } else if (!user) {
      setLoading(false);
      setPermissions([]);
    }
  }, [user])

  const fetchPermissions = async () => {
    if (!user || !user.role) return;
    setLoading(true);

    try {
      const { data: role } = await supabase
        .from('roles')
        .select('id')
        .eq('name', user.role)
        .single()

      let rolePermissionsList: Permission[] = [];
      if (role) {
        const { data: rolePerms } = await supabase
          .from('role_permissions')
          .select('permissions(*)')
          .eq('role_id', role.id)
          .eq('is_active', true);
        
        rolePermissionsList = rolePerms?.map(rp => rp.permissions).filter(Boolean) || [];
      }
      
      console.log(`🔐 Permissions loaded for role '${user.role}':`, rolePermissionsList);
      console.log('👤 User object in usePermissions:', user);
      setPermissions(rolePermissionsList);

    } catch (error) {
      console.error('Error fetching permissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const hasPermission = (module: string, action: 'view' | 'add' | 'edit' | 'delete') => {
    const modulePermissions = permissions.filter(p => p.module === module);
    if (modulePermissions.length === 0) {
      return false;
    }
    
    return modulePermissions.some(p => {
      switch (action) {
        case 'view': return p.can_view;
        case 'add': return p.can_add;
        case 'edit': return p.can_edit;
        case 'delete': return p.can_delete;
        default: return false;
      }
    });
  }
  
  const canAccess = (module: string) => {
    return hasPermission(module, 'view');
  };

  return {
    permissions,
    loading,
    hasPermission,
    canAccess,
    userRole: user?.role || null
  };
}