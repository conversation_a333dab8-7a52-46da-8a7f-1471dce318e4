import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Plus<PERSON>ircle,
  Search,
  FileDown,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Users,
  Layout
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

const ClientsManagement = () => {
  const navigate = useNavigate();
  const [clients, setClients] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [clientToDelete, setClientToDelete] = useState<any | null>(null);
  const { hasPermission, canAccess, loading: permissionsLoading, userRole } = usePermissions(); 
  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('clients')
        .select(`
          *,
          investments(amount, team_members)
        `)
        .eq('is_deleted', false);

      const { data, error } = await query;
      if (error) throw error;

      let filteredData = data || [];

      // Apply role-based filtering for team members
      if (userRole === 'team_member') {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        if (currentUser?.id && filteredData.length > 0) {
          const validClientIds = [];
          
          for (const client of filteredData) {
            // Check if client was created by current user
            if (client.created_by === currentUser.id) {
              validClientIds.push(client.id);
              continue;
            }
            
            // Check if client has team_members field
            if (client.team_members) {
              try {
                const teamMembers = JSON.parse(client.team_members);
                const teamMemberIds = teamMembers.map((tm: any) => tm.id);
                
                if (teamMemberIds.length > 0) {
                  const { data: matchingMembers } = await supabase
                    .from('team_members')
                    .select('id, user_id')
                    .in('id', teamMemberIds)
                    .eq('user_id', currentUser.id);
                  
                  if (matchingMembers && matchingMembers.length > 0) {
                    validClientIds.push(client.id);
                    continue;
                  }
                }
              } catch (e) {
                console.error('Error parsing client team_members JSON:', e);
              }
            }
            
            // Check investments for team member association
            if (client.investments && client.investments.length > 0) {
              for (const investment of client.investments) {
                if (investment.team_members) {
                  try {
                    const teamMembers = JSON.parse(investment.team_members);
                    const teamMemberIds = teamMembers.map((tm: any) => tm.id);
                    
                    if (teamMemberIds.length > 0) {
                      const { data: matchingMembers } = await supabase
                        .from('team_members')
                        .select('id, user_id')
                        .in('id', teamMemberIds)
                        .eq('user_id', currentUser.id);
                      
                      if (matchingMembers && matchingMembers.length > 0) {
                        validClientIds.push(client.id);
                        break;
                      }
                    }
                  } catch (e) {
                    console.error('Error parsing investment team_members JSON:', e);
                  }
                }
              }
            }
          }
          
          filteredData = filteredData.filter(client => validClientIds.includes(client.id));
        } else {
          filteredData = [];
        }
      }

      const clientsWithStats = filteredData?.map(client => ({
        ...client,
        name: `${client.first_name} ${client.last_name || ''}`.trim(),
        totalInvestment: client.investments?.reduce((sum: number, inv: any) => sum + (inv.amount || 0), 0) || 0,
        activeSchemes: client.investments?.length || 0,
        status: client.is_active ? 'Active' : 'Inactive',
        joinDate: new Date(client.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
      })) || [];

      setClients(clientsWithStats);
    } catch (error: any) {
      toast.error('Failed to fetch clients: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async () => {
    if (!clientToDelete) return;
    try {
      const { error } = await supabase
        .from('clients')
        .update({ is_deleted: true })
        .eq('id', clientToDelete.id);

      if (error) throw error;

      toast.success(`Client "${clientToDelete.name}" has been deleted.`);
      setClients(clients.filter(c => c.id !== clientToDelete.id));
      setClientToDelete(null);
    } catch (error: any) {
      toast.error('Failed to delete client: ' + error.message);
    }
  };

  const filteredClients = clients.filter(client =>
    client.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const SkeletonRow = () => (
    <TableRow>
      <TableCell>
        <div className="flex items-center space-x-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-1">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      </TableCell>
      <TableCell className="hidden lg:table-cell"><Skeleton className="h-4 w-32" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell className="hidden sm:table-cell"><Skeleton className="h-6 w-12 rounded-full" /></TableCell>
      <TableCell className="hidden lg:table-cell"><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
      <TableCell className="hidden xl:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
      <TableCell><Skeleton className="h-8 w-8" /></TableCell>
    </TableRow>
  );

  if (permissionsLoading) {
    return <div className="flex items-center justify-center h-64">Loading permissions...</div>;
  }

  if (!canAccess('clients')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view clients.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        <div className="mx-auto">
          <header className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-800">Clients Management</h1>
              <p className="text-sm text-gray-500 mt-1">Manage all your clients and their investments.</p>
            </div>
            {hasPermission('clients', 'add') && (
              <Button onClick={() => navigate('/admin/clients/add')} className="shadow-sm w-full sm:w-auto">
                <PlusCircle className="h-4 w-4 mr-2" />
                Add New Client
              </Button>
            )}
          </header>

          <Card className="shadow-sm mt-4">
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
                <div className="relative flex-1 md:grow-0">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full md:w-80"
                  />
                </div>
                <div className="flex items-center gap-2">
                  {hasPermission('clients', 'delete') && (
                    <Button variant="outline" size="sm">
                      <FileDown className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Mobile Card View */}
              <div className="block md:hidden space-y-4">
                {loading ? (
                  Array.from({ length: 3 }).map((_, i) => (
                    <Card key={i} className="p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-1 flex-1">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-24" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    </Card>
                  ))
                ) : filteredClients.length > 0 ? (
                  filteredClients.map((client) => (
                    <Card key={client.id} className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          {client.client_photo_url ? (
                            <img
                              src={client.client_photo_url}
                              alt={client.name}
                              className="h-12 w-12 rounded-full object-cover border-2 border-gray-200"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded-full bg-primary/10 text-primary flex items-center justify-center font-bold text-lg">
                              {client.name.charAt(0)}
                            </div>
                          )}
                          <div>
                            <p className="font-semibold text-gray-800">{client.name}</p>
                            <p className="text-xs text-gray-500">{client.client_code || 'No code'}</p>
                          </div>
                        </div>
                        <Badge variant={client.status === 'Active' ? 'default' : 'outline'} className="capitalize">
                          <span className={`h-2 w-2 mr-1 rounded-full ${client.status === 'Active' ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                          {client.status}
                        </Badge>
                      </div>

                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Email:</span>
                          <span className="font-medium">{client.email}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Phone:</span>
                          <span className="font-medium">{client.mobile_number}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Investment:</span>
                          <span className="font-mono font-medium">₹{client.totalInvestment.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Schemes:</span>
                          <Badge variant="secondary">{client.activeSchemes}</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Joined:</span>
                          <span className="font-medium">{client.joinDate}</span>
                        </div>
                      </div>

                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate(`/admin/clients/view/${client.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        {hasPermission('clients', 'edit') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/admin/clients/edit/${client.id}`)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        )}
                        {hasPermission('clients', 'delete') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setClientToDelete(client)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Clients Found</h3>
                    <p className="text-gray-500">No clients match your search. Try a different query.</p>
                  </div>
                )}
              </div>

              {/* Desktop Table View */}
              <div className="hidden md:block rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Client</TableHead>
                      <TableHead className="hidden lg:table-cell">Contact</TableHead>
                      <TableHead className="text-right">Investment</TableHead>
                      <TableHead className="text-center hidden sm:table-cell">Schemes</TableHead>
                      <TableHead className="hidden lg:table-cell">Status</TableHead>
                      <TableHead className="hidden xl:table-cell">Join Date</TableHead>
                      <TableHead><span className="sr-only">Actions</span></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      Array.from({ length: 5 }).map((_, i) => <SkeletonRow key={i} />)
                    ) : filteredClients.length > 0 ? (
                      filteredClients.map((client) => (
                        <TableRow key={client.id} className="hover:bg-gray-50">
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              {client.client_photo_url ? (
                                <img
                                  src={client.client_photo_url}
                                  alt={client.name}
                                  className="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-full bg-primary/10 text-primary flex items-center justify-center font-bold">
                                  {client.name.charAt(0)}
                                </div>
                              )}
                              <div className="min-w-0">
                                <p className="font-semibold text-gray-800 truncate">{client.name}</p>
                                <p className="text-xs text-gray-500 truncate">{client.client_code || 'No code'}</p>
                                <div className="lg:hidden">
                                  <p className="text-xs text-gray-600 truncate">{client.email}</p>
                                  <Badge variant={client.status === 'Active' ? 'default' : 'outline'} className="text-xs mt-1">
                                    {client.status}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            <p className="text-sm font-medium text-gray-700 truncate">{client.email}</p>
                            <p className="text-xs text-gray-500">{client.mobile_number}</p>
                          </TableCell>
                          <TableCell className="text-right">
                            <span className="font-mono text-sm font-medium">₹{client.totalInvestment.toLocaleString()}</span>
                          </TableCell>
                          <TableCell className="text-center hidden sm:table-cell">
                            <Badge variant="secondary" className="font-medium">{client.activeSchemes}</Badge>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            <Badge variant={client.status === 'Active' ? 'default' : 'outline'} className="capitalize">
                              <span className={`h-2 w-2 mr-2 rounded-full ${client.status === 'Active' ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                              {client.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden xl:table-cell">
                            <span className="text-sm text-gray-600">{client.joinDate}</span>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => navigate(`/admin/clients/view/${client.id}`)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View
                                </DropdownMenuItem>
                                {hasPermission('clients', 'edit') && (
                                  <DropdownMenuItem onClick={() => navigate(`/admin/clients/edit/${client.id}`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                )}
                                {hasPermission('clients', 'delete') && (
                                  <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() => setClientToDelete(client)}
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete
                                    </DropdownMenuItem>
                                  </>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="h-48 text-center">
                          <div className="flex flex-col items-center justify-center gap-4">
                            <Users className="h-12 w-12 text-gray-400" />
                            <h3 className="text-xl font-semibold text-gray-700">No Clients Found</h3>
                            <p className="text-gray-500">No clients match your search. Try a different query.</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>

        <AlertDialog open={!!clientToDelete} onOpenChange={() => setClientToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will mark the client "{clientToDelete?.name}" as deleted. This is a soft delete and the data can be recovered.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteClient} className="bg-red-600 hover:bg-red-700">
                Yes, Delete Client
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
  );
};

export default ClientsManagement;

