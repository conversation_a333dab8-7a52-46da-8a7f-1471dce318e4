import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface CompanySettings {
  company_name: string;
  company_logo_url: string;
}

export const useCompanySettings = () => {
  const [settings, setSettings] = useState<CompanySettings>({
    company_name: 'CareCapital',
    company_logo_url: ''
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value')
        .in('setting_key', ['company_name', 'company_logo_url'])
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;

      const settingsObj = data?.reduce((acc, setting) => {
        acc[setting.setting_key] = setting.setting_value || '';
        return acc;
      }, {} as any) || {};

      setSettings(prev => ({ ...prev, ...settingsObj }));
    } catch (error) {
      console.error('Error fetching company settings:', error);
    } finally {
      setLoading(false);
    }
  };

  return { settings, loading };
};