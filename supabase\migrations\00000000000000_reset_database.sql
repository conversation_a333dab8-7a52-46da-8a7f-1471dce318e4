-- COMPLETE DATABASE RESET - Drop everything and recreate from scratch
-- WARNING: This will delete ALL data

-- Drop all existing policies first
DO $$ 
DECLARE
    r RECORD;
BEGIN
    -- Drop all RLS policies
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Drop all triggers
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT trigger_name, event_object_table FROM information_schema.triggers WHERE trigger_schema = 'public') LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || r.trigger_name || ' ON public.' || r.event_object_table;
    END LOOP;
END $$;

-- Drop all functions
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT proname, oidvectortypes(proargtypes) as argtypes FROM pg_proc INNER JOIN pg_namespace ns ON (pg_proc.pronamespace = ns.oid) WHERE ns.nspname = 'public') LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS public.' || r.proname || '(' || r.argtypes || ') CASCADE';
    END LOOP;
END $$;

-- Drop all views
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT viewname FROM pg_views WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP VIEW IF EXISTS public.' || r.viewname || ' CASCADE';
    END LOOP;
END $$;

-- Drop all tables in correct order (reverse dependency)
DROP TABLE IF EXISTS public.activity_logs CASCADE;
DROP TABLE IF EXISTS public.alerts CASCADE;
DROP TABLE IF EXISTS public.blogs CASCADE;
DROP TABLE IF EXISTS public.broker_investments CASCADE;
DROP TABLE IF EXISTS public.broker_transactions CASCADE;
DROP TABLE IF EXISTS public.brokers CASCADE;
DROP TABLE IF EXISTS public.charity_donations CASCADE;
DROP TABLE IF EXISTS public.client_documents CASCADE;
DROP TABLE IF EXISTS public.clients CASCADE;
DROP TABLE IF EXISTS public.company_settings CASCADE;
DROP TABLE IF EXISTS public.contact_inquiries CASCADE;
DROP TABLE IF EXISTS public.investment_payments CASCADE;
DROP TABLE IF EXISTS public.investments CASCADE;
DROP TABLE IF EXISTS public.nominees CASCADE;
DROP TABLE IF EXISTS public.permissions CASCADE;
DROP TABLE IF EXISTS public.role_permissions CASCADE;
DROP TABLE IF EXISTS public.roles CASCADE;
DROP TABLE IF EXISTS public.schemes CASCADE;
DROP TABLE IF EXISTS public.stock_training_inquiries CASCADE;
DROP TABLE IF EXISTS public.team_members CASCADE;
DROP TABLE IF EXISTS public.user_roles CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Drop all custom types
DROP TYPE IF EXISTS public.alert_type CASCADE;
DROP TYPE IF EXISTS public.notification_channel CASCADE;
DROP TYPE IF EXISTS public.investment_status CASCADE;
DROP TYPE IF EXISTS public.payment_status CASCADE;
DROP TYPE IF EXISTS public.blog_status CASCADE;
DROP TYPE IF EXISTS public.transaction_type CASCADE;

-- Now recreate everything from main.sql

-- Create enums
CREATE TYPE public.alert_type AS ENUM ('maturity', 'payment_reminder', 'investment_due', 'system_notification');
CREATE TYPE public.notification_channel AS ENUM ('email', 'sms', 'whatsapp', 'push');
CREATE TYPE public.investment_status AS ENUM ('active', 'completed', 'closed', 'suspended');
CREATE TYPE public.payment_status AS ENUM ('pending', 'paid', 'overdue', 'cancelled');
CREATE TYPE public.blog_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE public.transaction_type AS ENUM ('interest_payout', 'investment_allocation', 'withdrawal', 'commission_payment');

-- Create tables in dependency order
CREATE TABLE public.roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT roles_pkey PRIMARY KEY (id)
);

CREATE TABLE public.permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  module character varying NOT NULL,
  can_view boolean DEFAULT false,
  can_add boolean DEFAULT false,
  can_edit boolean DEFAULT false,
  can_delete boolean DEFAULT false,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT permissions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.users (
  id uuid NOT NULL,
  username character varying UNIQUE,
  role_id uuid,
  mobile character varying UNIQUE,
  first_name character varying,
  last_name character varying,
  email character varying,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT users_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id)
);

CREATE TABLE public.role_permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  role_id uuid NOT NULL,
  permission_id uuid NOT NULL,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT role_permissions_pkey PRIMARY KEY (id),
  CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id),
  CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id)
);

CREATE TABLE public.user_roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  role_id uuid NOT NULL,
  assigned_at timestamp with time zone DEFAULT now(),
  assigned_by uuid,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT user_roles_pkey PRIMARY KEY (id),
  CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id)
);

CREATE TABLE public.team_members (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  refer_code character varying NOT NULL UNIQUE,
  first_name character varying NOT NULL,
  last_name character varying,
  email character varying UNIQUE,
  phone character varying UNIQUE,
  address text,
  city character varying,
  state character varying,
  pincode character varying,
  country character varying DEFAULT 'India'::character varying,
  account_number character varying,
  ifsc_code character varying,
  bank_name character varying,
  branch_name character varying,
  commission_pct numeric DEFAULT 0,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT team_members_pkey PRIMARY KEY (id),
  CONSTRAINT team_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT team_members_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT team_members_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT team_members_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);

CREATE TABLE public.clients (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  client_code character varying UNIQUE,
  first_name character varying NOT NULL,
  last_name character varying,
  email character varying,
  mobile_number character varying NOT NULL UNIQUE,
  aadhar_number character varying UNIQUE,
  pan_card_number character varying UNIQUE,
  address text,
  city character varying,
  state character varying,
  pincode character varying,
  country character varying DEFAULT 'India'::character varying,
  client_photo_url text,
  account_number character varying,
  ifsc_code character varying,
  bank_name character varying,
  branch_name character varying,
  referred_by uuid,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT clients_pkey PRIMARY KEY (id),
  CONSTRAINT clients_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT clients_referred_by_fkey FOREIGN KEY (referred_by) REFERENCES public.team_members(id),
  CONSTRAINT clients_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT clients_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT clients_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);

CREATE TABLE public.schemes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  min_investment numeric NOT NULL,
  max_investment numeric,
  tenure_months integer NOT NULL,
  monthly_interest_pct numeric NOT NULL,
  annual_interest_pct numeric,
  commission_pct numeric DEFAULT 0,
  lock_in_period integer DEFAULT 0,
  scheme_type character varying DEFAULT 'fixed'::character varying,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT schemes_pkey PRIMARY KEY (id),
  CONSTRAINT schemes_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT schemes_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.nominees (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  client_id uuid NOT NULL,
  name character varying NOT NULL,
  relationship character varying,
  phone character varying,
  email character varying,
  address text,
  share_percentage numeric DEFAULT 100.00,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT nominees_pkey PRIMARY KEY (id),
  CONSTRAINT nominees_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT nominees_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT nominees_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.investments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  investment_code character varying UNIQUE,
  client_id uuid NOT NULL,
  scheme_id uuid NOT NULL,
  scheme_snapshot jsonb NOT NULL,
  amount numeric NOT NULL,
  start_date date NOT NULL,
  end_date date NOT NULL,
  monthly_interest numeric,
  total_expected_return numeric,
  team_members jsonb,
  total_commission_pct numeric DEFAULT 0,
  nominee_id uuid,
  status investment_status DEFAULT 'active'::investment_status,
  maturity_amount numeric,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT investments_pkey PRIMARY KEY (id),
  CONSTRAINT investments_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT investments_scheme_id_fkey FOREIGN KEY (scheme_id) REFERENCES public.schemes(id),
  CONSTRAINT investments_nominee_id_fkey FOREIGN KEY (nominee_id) REFERENCES public.nominees(id),
  CONSTRAINT investments_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT investments_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.investment_payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  investment_id uuid NOT NULL,
  month_number integer NOT NULL,
  due_date date NOT NULL,
  amount numeric NOT NULL,
  status payment_status DEFAULT 'pending'::payment_status,
  paid_at timestamp with time zone,
  paid_amount numeric,
  payment_method character varying,
  transaction_reference character varying,
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT investment_payments_pkey PRIMARY KEY (id),
  CONSTRAINT investment_payments_investment_id_fkey FOREIGN KEY (investment_id) REFERENCES public.investments(id),
  CONSTRAINT investment_payments_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT investment_payments_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.alerts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  investment_id uuid,
  user_id uuid,
  client_id uuid,
  alert_type alert_type NOT NULL,
  alert_date date NOT NULL,
  message text NOT NULL,
  status character varying DEFAULT 'pending'::character varying,
  channel notification_channel NOT NULL,
  sent_at timestamp with time zone,
  error_message text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT alerts_pkey PRIMARY KEY (id),
  CONSTRAINT alerts_investment_id_fkey FOREIGN KEY (investment_id) REFERENCES public.investments(id),
  CONSTRAINT alerts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT alerts_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT alerts_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT alerts_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT alerts_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);

CREATE TABLE public.blogs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title character varying NOT NULL,
  slug character varying NOT NULL UNIQUE,
  author_id uuid NOT NULL,
  category character varying,
  tags text,
  cover_image_url text,
  excerpt text,
  content text NOT NULL,
  status blog_status DEFAULT 'draft'::blog_status,
  published_at timestamp with time zone,
  seo_title character varying,
  seo_description text,
  seo_keywords text,
  view_count integer DEFAULT 0,
  is_featured boolean DEFAULT false,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT blogs_pkey PRIMARY KEY (id),
  CONSTRAINT blogs_author_id_fkey FOREIGN KEY (author_id) REFERENCES auth.users(id),
  CONSTRAINT blogs_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT blogs_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.brokers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  broker_code character varying NOT NULL UNIQUE,
  broker_name character varying NOT NULL,
  email character varying UNIQUE,
  phone character varying UNIQUE,
  address text,
  registration_number character varying,
  license_number character varying,
  commission_pct numeric DEFAULT 0,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT brokers_pkey PRIMARY KEY (id),
  CONSTRAINT brokers_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT brokers_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT brokers_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT brokers_user_id_fkey1 FOREIGN KEY (user_id) REFERENCES public.users(id)
);

CREATE TABLE public.broker_investments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  broker_id uuid NOT NULL,
  amount_invested numeric NOT NULL,
  start_date date NOT NULL,
  end_date date,
  expected_return_pct numeric,
  status investment_status DEFAULT 'active'::investment_status,
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT broker_investments_pkey PRIMARY KEY (id),
  CONSTRAINT broker_investments_broker_id_fkey FOREIGN KEY (broker_id) REFERENCES public.brokers(id),
  CONSTRAINT broker_investments_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT broker_investments_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.broker_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  broker_investment_id uuid NOT NULL,
  transaction_type transaction_type NOT NULL,
  amount numeric NOT NULL,
  transaction_date timestamp with time zone DEFAULT now(),
  reference_number character varying,
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT broker_transactions_pkey PRIMARY KEY (id),
  CONSTRAINT broker_transactions_broker_investment_id_fkey FOREIGN KEY (broker_investment_id) REFERENCES public.broker_investments(id),
  CONSTRAINT broker_transactions_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT broker_transactions_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.charity_donations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  donor_name character varying,
  donor_email character varying,
  donor_phone character varying,
  amount numeric NOT NULL,
  donation_method character varying,
  transaction_reference character varying,
  cause character varying,
  is_anonymous boolean DEFAULT false,
  message text,
  receipt_sent boolean DEFAULT false,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT charity_donations_pkey PRIMARY KEY (id)
);

CREATE TABLE public.client_documents (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  client_id uuid NOT NULL,
  document_type character varying NOT NULL,
  document_url text NOT NULL,
  document_name character varying,
  verified boolean DEFAULT false,
  verified_by uuid,
  verified_at timestamp with time zone,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT client_documents_pkey PRIMARY KEY (id),
  CONSTRAINT client_documents_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT client_documents_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES auth.users(id),
  CONSTRAINT client_documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT client_documents_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.company_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  setting_key character varying NOT NULL UNIQUE,
  setting_value text,
  setting_type character varying DEFAULT 'text'::character varying,
  category character varying,
  description text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT company_settings_pkey PRIMARY KEY (id),
  CONSTRAINT company_settings_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT company_settings_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

CREATE TABLE public.contact_inquiries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  email character varying NOT NULL,
  phone character varying,
  subject character varying,
  message text NOT NULL,
  inquiry_type character varying,
  status character varying DEFAULT 'new'::character varying,
  assigned_to uuid,
  response text,
  responded_at timestamp with time zone,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT contact_inquiries_pkey PRIMARY KEY (id),
  CONSTRAINT contact_inquiries_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES auth.users(id)
);

CREATE TABLE public.stock_training_inquiries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  email character varying NOT NULL,
  phone character varying NOT NULL,
  experience_level character varying,
  preferred_schedule character varying,
  message text,
  status character varying DEFAULT 'new'::character varying,
  follow_up_date date,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT stock_training_inquiries_pkey PRIMARY KEY (id)
);

CREATE TABLE public.activity_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  table_name character varying NOT NULL,
  record_id uuid,
  action character varying NOT NULL,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT activity_logs_pkey PRIMARY KEY (id),
  CONSTRAINT activity_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);

-- Create indexes
CREATE INDEX idx_users_role_id ON public.users(role_id);
CREATE INDEX idx_users_mobile ON public.users(mobile);
CREATE INDEX idx_clients_mobile ON public.clients(mobile_number);
CREATE INDEX idx_clients_referred_by ON public.clients(referred_by);
CREATE INDEX idx_clients_user_id ON public.clients(user_id);
CREATE INDEX idx_investments_client_id ON public.investments(client_id);
CREATE INDEX idx_investments_scheme_id ON public.investments(scheme_id);
CREATE INDEX idx_investments_status ON public.investments(status);
CREATE INDEX idx_investment_payments_investment_id ON public.investment_payments(investment_id);
CREATE INDEX idx_investment_payments_status ON public.investment_payments(status);
CREATE INDEX idx_investment_payments_due_date ON public.investment_payments(due_date);
CREATE INDEX idx_alerts_alert_date ON public.alerts(alert_date);
CREATE INDEX idx_alerts_status ON public.alerts(status);
CREATE INDEX idx_alerts_user_id ON public.alerts(user_id);
CREATE INDEX idx_alerts_client_id ON public.alerts(client_id);
CREATE INDEX idx_blogs_status ON public.blogs(status);
CREATE INDEX idx_blogs_published_at ON public.blogs(published_at);
CREATE INDEX idx_blogs_author_id ON public.blogs(author_id);
CREATE INDEX idx_activity_logs_user_id ON public.activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON public.activity_logs(created_at);
CREATE INDEX idx_team_members_refer_code ON public.team_members(refer_code);
CREATE INDEX idx_brokers_broker_code ON public.brokers(broker_code);
CREATE INDEX idx_broker_investments_broker_id ON public.broker_investments(broker_id);
CREATE INDEX idx_broker_transactions_broker_investment_id ON public.broker_transactions(broker_investment_id);
CREATE INDEX idx_nominees_client_id ON public.nominees(client_id);
CREATE INDEX idx_client_documents_client_id ON public.client_documents(client_id);
CREATE INDEX idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON public.role_permissions(permission_id);
CREATE INDEX idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON public.user_roles(role_id);

-- Enable RLS
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.broker_investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.broker_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.brokers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.charity_donations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investment_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.nominees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schemes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_training_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create functions
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT r.name FROM public.users u 
    JOIN public.roles r ON u.role_id = r.id 
    WHERE u.id = auth.uid() AND u.is_active = true AND u.is_deleted = false;
$$;

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT public.get_current_user_role() = 'admin';
$$;

CREATE OR REPLACE FUNCTION public.is_team_member()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT public.get_current_user_role() IN ('admin', 'team_member');
$$;

CREATE OR REPLACE FUNCTION public.has_permission(module_name TEXT, permission_type TEXT)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
    SELECT EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.user_roles ur ON u.id = ur.user_id
        JOIN public.role_permissions rp ON ur.role_id = rp.role_id
        JOIN public.permissions p ON rp.permission_id = p.id
        WHERE u.id = auth.uid() 
        AND u.is_active = true 
        AND u.is_deleted = false
        AND ur.is_active = true 
        AND ur.is_deleted = false
        AND rp.is_active = true 
        AND rp.is_deleted = false
        AND p.is_active = true 
        AND p.is_deleted = false
        AND p.module = module_name
        AND (
            (permission_type = 'view' AND p.can_view = true) OR
            (permission_type = 'add' AND p.can_add = true) OR
            (permission_type = 'edit' AND p.can_edit = true) OR
            (permission_type = 'delete' AND p.can_delete = true)
        )
    );
$$;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    client_role_id UUID;
BEGIN
    -- Get client role ID, create if doesn't exist
    SELECT id INTO client_role_id FROM public.roles WHERE name = 'client';
    
    IF client_role_id IS NULL THEN
        INSERT INTO public.roles (name, description) 
        VALUES ('client', 'Client user with restricted access')
        RETURNING id INTO client_role_id;
    END IF;
    
    -- Insert into users table
    INSERT INTO public.users (id, email, role_id, created_at, updated_at)
    VALUES (NEW.id, NEW.email, client_role_id, now(), now())
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        role_id = EXCLUDED.role_id,
        updated_at = now();
    
    -- Insert into user_roles table
    INSERT INTO public.user_roles (user_id, role_id, assigned_by, created_at, updated_at)
    VALUES (NEW.id, client_role_id, NEW.id, now(), now())
    ON CONFLICT (user_id, role_id) DO NOTHING;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Error in handle_new_user: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT r.name 
  FROM users u
  JOIN roles r ON u.role_id = r.id
  WHERE u.id = user_id
  AND u.is_active = true 
  AND u.is_deleted = false
  LIMIT 1;
$$;

-- Create triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON public.permissions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_role_permissions_updated_at BEFORE UPDATE ON public.role_permissions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON public.user_roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_team_members_updated_at BEFORE UPDATE ON public.team_members FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_client_documents_updated_at BEFORE UPDATE ON public.client_documents FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_nominees_updated_at BEFORE UPDATE ON public.nominees FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_schemes_updated_at BEFORE UPDATE ON public.schemes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_investments_updated_at BEFORE UPDATE ON public.investments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_investment_payments_updated_at BEFORE UPDATE ON public.investment_payments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_brokers_updated_at BEFORE UPDATE ON public.brokers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_broker_investments_updated_at BEFORE UPDATE ON public.broker_investments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_broker_transactions_updated_at BEFORE UPDATE ON public.broker_transactions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_company_settings_updated_at BEFORE UPDATE ON public.company_settings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_alerts_updated_at BEFORE UPDATE ON public.alerts FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_blogs_updated_at BEFORE UPDATE ON public.blogs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create profiles view for compatibility
CREATE OR REPLACE VIEW public.profiles AS
SELECT 
    id,
    email,
    first_name,
    last_name,
    mobile as phone,
    created_at,
    updated_at
FROM public.users;

-- Create RLS policies
CREATE POLICY "Enable read for authenticated users" ON public.users FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.team_members FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.team_members FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.team_members FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.clients FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.clients FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.clients FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.roles FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.permissions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.role_permissions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable read for authenticated users" ON public.user_roles FOR SELECT USING (auth.role() = 'authenticated');

-- Insert only roles data
INSERT INTO public.roles (name, description) VALUES
('admin', 'System Administrator with full access'),
('team_member', 'Team member with limited access'),
('client', 'Client user with restricted access')
ON CONFLICT (name) DO NOTHING;