
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, TrendingUp, Bell, DollarSign } from "lucide-react";
import { LineChart, Line, ResponsiveContainer, XAxis, YAxis, Tooltip } from "recharts";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

const TeamDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState({
    myClients: 0,
    totalInvestments: 0,
    commissionEarned: 0,
    pendingApprovals: 0,
    monthlyData: [] as any[],
    recentActivity: [] as any[]
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      fetchTeamDashboardData();
    }
  }, [user])
  console.log(user.id);

  const fetchTeamDashboardData = async () => {
    try {
      // Get current team member ID
      const { data: teamMember, error: teamMemberError } = await supabase
        .from('team_members')
        .select('id, refer_code')
        .eq('user_id', user?.id)
        .maybeSingle();

      console.log('Team member lookup:', { teamMember, teamMemberError, userId: user?.id });

      if (teamMemberError || !teamMember) {
        console.log('No team member found for user:', user?.id);
        toast.error('Team member profile not found. Please contact admin to set up your team member profile.');
        setDashboardData({
          myClients: 0,
          totalInvestments: 0,
          commissionEarned: 0,
          pendingApprovals: 0,
          monthlyData: Array.from({ length: 6 }, (_, i) => {
            const date = new Date();
            date.setMonth(date.getMonth() - (5 - i));
            return { name: date.toLocaleDateString('en', { month: 'short' }), value: 0 };
          }),
          recentActivity: []
        });
        setLoading(false);
        return;
      }

      // Fetch ALL investments and filter by team member (same logic as InvestmentManagement)
      const { data: allInvestments } = await supabase
        .from('investments')
        .select(`
          *,
          clients(first_name, last_name, email)
        `)
        .eq('is_deleted', false);

      console.log('All investments fetched:', allInvestments?.length);

      // Filter investments where this team member is involved
      const teamMemberInvestments = [];
      if (allInvestments) {
        for (const investment of allInvestments) {
          if (investment.team_members) {
            try {
              const teamMembers = JSON.parse(investment.team_members);
              const teamMemberIds = teamMembers.map((tm: any) => tm.id);
              
              if (teamMemberIds.includes(teamMember.id)) {
                teamMemberInvestments.push(investment);
              }
            } catch (e) {
              console.error('Error parsing team_members JSON:', e);
            }
          }
        }
      }

      console.log('Filtered team member investments:', teamMemberInvestments.length);

      // Get unique clients from these investments
      const uniqueClientIds = [...new Set(teamMemberInvestments.map(inv => inv.client_id))];
      const clientsCount = uniqueClientIds.length;

      // Calculate commission earned this month
      const currentMonth = new Date();
      const monthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      
      let commissionThisMonth = 0;
      teamMemberInvestments.forEach(inv => {
        if (inv.status === 'active' && new Date(inv.created_at) >= monthStart) {
          try {
            const teamMembers = JSON.parse(inv.team_members || '[]');
            const memberCommission = teamMembers.find((tm: any) => tm.id === teamMember.id);
            if (memberCommission) {
              commissionThisMonth += memberCommission.commission_amount || 0;
            }
          } catch (e) {
            console.error('Error parsing team_members:', e);
          }
        }
      });

      // Count pending approvals from team member investments
      const pendingCount = teamMemberInvestments.filter(inv => inv.status === 'pending').length;

      console.log('Commission this month:', commissionThisMonth);
      console.log('Pending count:', pendingCount);

      // Monthly performance data
      const monthlyData = Array.from({ length: 6 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() - (5 - i));
        const monthName = date.toLocaleDateString('en', { month: 'short' });
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
        
        const monthTotal = teamMemberInvestments.filter(inv => {
          const invDate = new Date(inv.created_at);
          return invDate >= monthStart && invDate <= monthEnd && inv.status === 'active';
        }).reduce((sum, inv) => sum + inv.amount, 0) || 0;
        
        return { name: monthName, value: monthTotal };
      });

      // Recent client activity from team member investments
      const recentActivity = teamMemberInvestments
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 4);

      setDashboardData({
        myClients: clientsCount,
        totalInvestments: teamMemberInvestments.filter(inv => inv.status === 'active').reduce((sum, inv) => sum + inv.amount, 0),
        commissionEarned: commissionThisMonth,
        pendingApprovals: pendingCount,
        monthlyData,
        recentActivity
      });

      console.log('Final dashboard data:', {
        myClients: clientsCount,
        totalInvestments: teamMemberInvestments.filter(inv => inv.status === 'active').reduce((sum, inv) => sum + inv.amount, 0),
        commissionEarned: commissionThisMonth,
        pendingApprovals: pendingCount
      });
    } catch (error: any) {
      toast.error('Failed to fetch dashboard data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) return `₹${(amount / 10000000).toFixed(1)}Cr`;
    if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
    return `₹${amount.toLocaleString()}`;
  };
  return (
    <div className="space-y-8">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold text-primary">Team Dashboard</h1>
          <p className="text-muted-foreground">Manage your referred clients and their investments</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            Alerts (2)
          </Button>
        </div>
      </header>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">My Clients</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : dashboardData.myClients}</h2>
              <p className="text-green-600 text-sm">Referred clients</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Investments</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : formatCurrency(dashboardData.totalInvestments)}</h2>
              <p className="text-green-600 text-sm">Client investments</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Commission Earned</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : formatCurrency(dashboardData.commissionEarned)}</h2>
              <p className="text-blue-600 text-sm">This month</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Pending Approvals</p>
              <h2 className="text-3xl font-bold">{loading ? '...' : dashboardData.pendingApprovals}</h2>
              <p className="text-orange-600 text-sm">Need attention</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Bell className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">My Team Performance</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dashboardData.monthlyData}>
                <XAxis dataKey="name" stroke="#888888" />
                <YAxis stroke="#888888" />
                <Tooltip formatter={(value) => [formatCurrency(value as number), 'Investment']} />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#8884d8"
                  strokeWidth={3}
                  dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Client Activity</h3>
          <div className="space-y-4">
            {dashboardData.recentActivity.map((activity, i) => (
              <div key={i} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                    {activity.clients?.first_name?.charAt(0) || 'U'}
                  </div>
                  <div>
                    <p className="font-medium">{activity.clients?.first_name} {activity.clients?.last_name}</p>
                    <p className="text-sm text-muted-foreground">
                      {activity.status === 'pending' ? 'Pending Approval' : 'New Investment'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(activity.amount)}</p>
                  <p className="text-sm text-muted-foreground">{new Date(activity.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            ))}
            {dashboardData.recentActivity.length === 0 && !loading && (
              <p className="text-center text-muted-foreground py-4">No recent activity</p>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TeamDashboard;
