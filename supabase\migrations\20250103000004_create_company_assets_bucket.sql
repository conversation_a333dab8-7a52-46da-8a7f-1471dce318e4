-- Create company-assets bucket
INSERT INTO storage.buckets (id, name, public) VALUES ('company-assets', 'company-assets', true);

-- Create policy for public read access
CREATE POLICY "Public read access for company assets" ON storage.objects
FOR SELECT USING (bucket_id = 'company-assets');

-- Create policy for authenticated upload
CREATE POLICY "Authenticated users can upload company assets" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'company-assets' AND auth.role() = 'authenticated');

-- Create policy for authenticated update
CREATE POLICY "Authenticated users can update company assets" ON storage.objects
FOR UPDATE USING (bucket_id = 'company-assets' AND auth.role() = 'authenticated');

-- Create policy for authenticated delete
CREATE POLICY "Authenticated users can delete company assets" ON storage.objects
FOR DELETE USING (bucket_id = 'company-assets' AND auth.role() = 'authenticated');