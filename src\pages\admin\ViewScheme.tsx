import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, TrendingUp, Calendar, DollarSign, Percent, Clock } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

const ViewScheme = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [scheme, setScheme] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchScheme();
    }
  }, [id]);

  const fetchScheme = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select(`
          *,
          investments(amount, status, created_at)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      const schemeWithStats = {
        ...data,
        totalInvestments: data.investments?.reduce((sum: number, inv: any) => sum + (inv.amount || 0), 0) || 0,
        activeInvestments: data.investments?.length || 0,
        status: data.is_active ? 'Active' : 'Inactive',
        createdDate: new Date(data.created_at).toLocaleDateString()
      };

      setScheme(schemeWithStats);
    } catch (error: any) {
      toast.error('Failed to fetch scheme: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading scheme...</div>
      </div>
    );
  }

  if (!scheme) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Scheme not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <header className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => navigate("/admin/schemes")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Schemes
          </Button>
          <div>
            <h1 className="text-4xl font-bold text-primary">{scheme.name}</h1>
            <p className="text-muted-foreground">Scheme Details</p>
          </div>
        </div>
        <Button onClick={() => navigate(`/admin/schemes/edit/${scheme.id}`)}>
          <Edit className="h-4 w-4 mr-2" />
          Edit Scheme
        </Button>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Basic Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div>
              <p className="font-medium text-lg">{scheme.name}</p>
              <p className="text-sm text-muted-foreground">{scheme.description || 'No description'}</p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Scheme Type:</span>
                <Badge variant="outline" className="capitalize">{scheme.scheme_type}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge variant={scheme.is_active ? 'default' : 'secondary'}>
                  {scheme.status}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Created:</span>
                <span className="text-sm font-medium">{scheme.createdDate}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Investment Details */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Investment Details</h3>
          <div className="space-y-4">
            <div className="text-center p-4 bg-primary/10 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <DollarSign className="h-5 w-5 text-primary" />
                <span className="text-sm text-muted-foreground">Investment Range</span>
              </div>
              <p className="text-lg font-bold text-primary">
                ₹{scheme.min_investment?.toLocaleString()} - ₹{scheme.max_investment?.toLocaleString()}
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-lg font-semibold">{scheme.tenure_months}</p>
                <p className="text-xs text-muted-foreground">Months</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Clock className="h-4 w-4 text-orange-600" />
                </div>
                <p className="text-lg font-semibold">{scheme.lock_in_period || 0}</p>
                <p className="text-xs text-muted-foreground">Lock-in Months</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Interest & Returns */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Interest & Returns</h3>
          <div className="space-y-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span className="text-sm text-muted-foreground">Monthly Interest</span>
              </div>
              <p className="text-2xl font-bold text-green-600">{scheme.monthly_interest_pct}%</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Percent className="h-4 w-4 text-purple-600" />
                </div>
                <p className="text-lg font-semibold">{scheme.annual_interest_pct || 'N/A'}%</p>
                <p className="text-xs text-muted-foreground">Annual Interest</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-lg font-semibold">{scheme.commission_pct || 0}%</p>
                <p className="text-xs text-muted-foreground">Commission</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Investment Statistics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Investment Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">₹{scheme.totalInvestments.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">Total Investments</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <p className="text-2xl font-bold text-green-600">{scheme.activeInvestments}</p>
            <p className="text-sm text-muted-foreground">Active Investments</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <p className="text-2xl font-bold text-purple-600">
              ₹{((scheme.totalInvestments * scheme.monthly_interest_pct) / 100).toLocaleString()}
            </p>
            <p className="text-sm text-muted-foreground">Monthly Interest Payout</p>
          </div>
        </div>
      </Card>

      {/* Recent Investments */}
      {scheme.investments?.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Investments</h3>
          <div className="space-y-2">
            {scheme.investments.slice(0, 5).map((investment: any, index: number) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">₹{investment.amount.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(investment.created_at).toLocaleDateString()}
                  </p>
                </div>
                <Badge variant={investment.status === 'active' ? 'default' : 'secondary'}>
                  {investment.status}
                </Badge>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default ViewScheme;