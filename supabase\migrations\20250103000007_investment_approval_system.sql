-- Investment Approval System Migration

-- Update investment_status enum to include pending
ALTER TYPE public.investment_status ADD VALUE 'pending';
COMMIT;

-- Add approval fields to investments table
ALTER TABLE public.investments 
ADD COLUMN IF NOT EXISTS approved_by uuid REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS approved_at timestamp with time zone;

-- Update default status to pending
ALTER TABLE public.investments 
ALTER COLUMN status SET DEFAULT 'pending'::investment_status;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_investments_approved_by ON public.investments(approved_by);
CREATE INDEX IF NOT EXISTS idx_investments_approved_at ON public.investments(approved_at);

-- Approval function
BEGIN;

CREATE OR REPLACE FUNCTION public.approve_investment(investment_id uuid, approver_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    inv RECORD;
    scheme RECORD;
BEGIN
    SELECT * INTO inv FROM investments WHERE id = investment_id AND status = 'pending';
    IF NOT FOUND THEN RAISE EXCEPTION 'Investment not found or not pending'; END IF;
    
    SELECT * INTO scheme FROM jsonb_to_record(inv.scheme_snapshot) AS x(tenure_months int, name text);
    
    UPDATE investments SET status = 'active', approved_by = approver_id, approved_at = now() WHERE id = investment_id;
    
    INSERT INTO investment_payments (investment_id, month_number, due_date, amount, status)
    SELECT investment_id, s, (inv.start_date + (s || ' months')::interval)::date, inv.monthly_interest, 'pending'
    FROM generate_series(1, scheme.tenure_months) s;
    
    INSERT INTO transactions (transaction_code, investment_id, client_id, transaction_type, amount, description, reference_number, payment_method, status)
    VALUES ('TXN' || extract(epoch from now())::bigint, investment_id, inv.client_id, 'investment_allocation', inv.amount, 'Investment in ' || scheme.name, inv.investment_code, 'bank_transfer', 'completed');
END;
$$;

COMMIT;