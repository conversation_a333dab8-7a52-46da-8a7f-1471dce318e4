import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Building2, Save, Upload, X } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

interface CompanySetting {
  id: string;
  setting_key: string;
  setting_value: string;
  setting_type: string;
  category: string;
}

const CompanySettings = () => {
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [settings, setSettings] = useState({
    company_name: "",
    company_email: "",
    company_phone: "",
    company_address: "",
    company_tagline: "",
    company_logo_url: ""
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('company_settings')
        .select('*')
        .in('setting_key', ['company_name', 'company_email', 'company_phone', 'company_address', 'company_tagline', 'company_logo_url'])
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;

      const settingsObj = data?.reduce((acc, setting) => {
        acc[setting.setting_key] = setting.setting_value || '';
        return acc;
      }, {} as any) || {};

      setSettings(prev => ({ ...prev, ...settingsObj }));
    } catch (error: any) {
      toast.error('Failed to fetch settings: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `logo-${Date.now()}.${fileExt}`;
      const filePath = `logos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('company-assets')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('company-assets')
        .getPublicUrl(filePath);

      setSettings(prev => ({ ...prev, company_logo_url: data.publicUrl }));
      toast.success('Logo uploaded successfully');
    } catch (error: any) {
      toast.error('Failed to upload logo: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const settingsToSave = [
        { key: 'company_name', value: settings.company_name, type: 'text', category: 'company', description: 'Company name' },
        { key: 'company_email', value: settings.company_email, type: 'email', category: 'company', description: 'Company email address' },
        { key: 'company_phone', value: settings.company_phone, type: 'text', category: 'company', description: 'Company phone number' },
        { key: 'company_address', value: settings.company_address, type: 'textarea', category: 'company', description: 'Company address' },
        { key: 'company_tagline', value: settings.company_tagline, type: 'text', category: 'company', description: 'Company tagline' },
        { key: 'company_logo_url', value: settings.company_logo_url, type: 'url', category: 'company', description: 'Company logo URL' }
      ];

      for (const setting of settingsToSave) {
        const { error } = await supabase
          .from('company_settings')
          .upsert({
            setting_key: setting.key,
            setting_value: setting.value,
            setting_type: setting.type,
            category: setting.category,
            description: setting.description,
            is_active: true,
            is_deleted: false
          }, {
            onConflict: 'setting_key'
          });

        if (error) throw error;
      }

      toast.success('Company settings saved successfully');
    } catch (error: any) {
      toast.error('Failed to save settings: ' + error.message);
    } finally {
      setSaving(false);
    }
  };

  if (permissionsLoading) {
    return <div className="flex items-center justify-center h-64">Loading permissions...</div>;
  }

  if (!canAccess('settings')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view settings.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      <header>
        <h1 className="text-3xl font-bold text-gray-900">Company Settings</h1>
        <p className="text-gray-600 mt-1">Manage your company information and branding</p>
      </header>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Company Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="company_name">Company Name *</Label>
              <Input
                id="company_name"
                value={settings.company_name}
                onChange={(e) => setSettings(prev => ({ ...prev, company_name: e.target.value }))}
                placeholder="Enter company name"
                disabled={!hasPermission('settings', 'edit')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_email">Company Email *</Label>
              <Input
                id="company_email"
                type="email"
                value={settings.company_email}
                onChange={(e) => setSettings(prev => ({ ...prev, company_email: e.target.value }))}
                placeholder="Enter company email"
                disabled={!hasPermission('settings', 'edit')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_phone">Company Phone *</Label>
              <Input
                id="company_phone"
                value={settings.company_phone}
                onChange={(e) => setSettings(prev => ({ ...prev, company_phone: e.target.value }))}
                placeholder="Enter company phone"
                disabled={!hasPermission('settings', 'edit')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_tagline">Company Tagline</Label>
              <Input
                id="company_tagline"
                value={settings.company_tagline}
                onChange={(e) => setSettings(prev => ({ ...prev, company_tagline: e.target.value }))}
                placeholder="Enter company tagline"
                disabled={!hasPermission('settings', 'edit')}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company_address">Company Address</Label>
            <Textarea
              id="company_address"
              value={settings.company_address}
              onChange={(e) => setSettings(prev => ({ ...prev, company_address: e.target.value }))}
              placeholder="Enter complete company address"
              rows={3}
              disabled={!hasPermission('settings', 'edit')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="company_logo">Company Logo</Label>
            <div className="flex gap-2">
              <Input
                id="company_logo_url"
                value={settings.company_logo_url}
                onChange={(e) => setSettings(prev => ({ ...prev, company_logo_url: e.target.value }))}
                placeholder="Enter logo image URL or upload file"
                disabled={!hasPermission('settings', 'edit')}
              />
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="hidden"
                id="logo-upload"
                disabled={!hasPermission('settings', 'edit')}
              />
              <Button 
                variant="outline" 
                size="sm" 
                disabled={!hasPermission('settings', 'edit') || uploading}
                onClick={() => document.getElementById('logo-upload')?.click()}
              >
                <Upload className="h-4 w-4" />
                {uploading ? 'Uploading...' : 'Upload'}
              </Button>
            </div>
            {settings.company_logo_url && (
              <div className="mt-2 relative inline-block">
                <img 
                  src={settings.company_logo_url} 
                  alt="Company Logo Preview" 
                  className="h-16 w-auto border rounded"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                {hasPermission('settings', 'edit') && (
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 p-0"
                    onClick={() => setSettings(prev => ({ ...prev, company_logo_url: '' }))}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            )}
          </div>

          {hasPermission('settings', 'edit') && (
            <div className="flex justify-end pt-4">
              <Button onClick={handleSave} disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CompanySettings;