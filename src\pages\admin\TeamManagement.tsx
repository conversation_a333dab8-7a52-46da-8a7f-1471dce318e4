
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import {
  PlusCircle,
  Search,
  FileDown,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Users
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

const TeamManagement = () => {
  const navigate = useNavigate();
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [memberToDelete, setMemberToDelete] = useState<any | null>(null);
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const fetchTeamMembers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select(`
          *,
          clients!clients_referred_by_fkey(id)
        `)
        .eq('is_deleted', false);

      if (error) throw error;

      const membersWithStats = data?.map(member => ({
        ...member,
        name: `${member.first_name} ${member.last_name || ''}`.trim(),
        totalClients: member.clients?.length || 0,
        status: member.is_active ? 'Active' : 'Inactive',
        joinDate: new Date(member.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
      })) || [];

      setTeamMembers(membersWithStats);
    } catch (error: any) {
      toast.error('Failed to fetch team members: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMember = async () => {
    if (!memberToDelete) return;
    try {
      const { error } = await supabase
        .from('team_members')
        .update({ is_deleted: true })
        .eq('id', memberToDelete.id);

      if (error) throw error;

      toast.success(`Team member "${memberToDelete.name}" has been deleted.`);
      setTeamMembers(teamMembers.filter(m => m.id !== memberToDelete.id));
      setMemberToDelete(null);
    } catch (error: any) {
      toast.error('Failed to delete team member: ' + error.message);
    }
  };

  const filteredMembers = teamMembers.filter(member =>
    member.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );


  const SkeletonRow = () => (
    <TableRow>
      <TableCell>
        <div className="flex items-center space-x-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-1">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      </TableCell>
      <TableCell className="hidden lg:table-cell"><Skeleton className="h-4 w-32" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell className="hidden sm:table-cell"><Skeleton className="h-6 w-12 rounded-full" /></TableCell>
      <TableCell className="hidden lg:table-cell"><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
      <TableCell className="hidden xl:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
      <TableCell><Skeleton className="h-8 w-8" /></TableCell>
    </TableRow>
  );

  if (permissionsLoading) {
    return <div>Loading permissions...</div>;
  }

  if (!canAccess('team')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground">You don't have permission to view schemes management.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="mx-auto">
        <header className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-800">Team Management</h1>
            <p className="text-sm text-gray-500 mt-1">Manage all your team members and their access.</p>
          </div>
          {hasPermission('team', 'add') && (
            <Button onClick={() => navigate('/admin/team/add')} className="shadow-sm w-full sm:w-auto">
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Team Member
            </Button>
          )}
        </header>

        <Card className="shadow-sm mt-4">
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
              <div className="relative flex-1 md:grow-0">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full md:w-80"
                />
              </div>
              <div className="flex items-center gap-2">
                {hasPermission('team', 'view') && (
                  <Button variant="outline" size="sm">
                    <FileDown className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Mobile Card View */}
            <div className="block md:hidden space-y-4">
              {loading ? (
                Array.from({ length: 3 }).map((_, i) => (
                  <Card key={i} className="p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="space-y-1 flex-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-3 w-full" />
                      <Skeleton className="h-3 w-3/4" />
                    </div>
                  </Card>
                ))
              ) : filteredMembers.length > 0 ? (
                filteredMembers.map((member) => (
                  <Card key={member.id} className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="h-12 w-12 rounded-full bg-primary/10 text-primary flex items-center justify-center font-bold text-lg">
                          {member.name.charAt(0)}
                        </div>
                        <div>
                          <p className="font-semibold text-gray-800">{member.name}</p>
                          <p className="text-xs text-gray-500">{member.refer_code || 'No code'}</p>
                        </div>
                      </div>
                      <Badge variant={member.status === 'Active' ? 'default' : 'outline'} className="capitalize">
                        <span className={`h-2 w-2 mr-1 rounded-full ${member.status === 'Active' ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                        {member.status}
                      </Badge>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Email:</span>
                        <span className="font-medium">{member.email}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Phone:</span>
                        <span className="font-medium">{member.phone}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Clients:</span>
                        <Badge variant="secondary">{member.totalClients}</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Commission:</span>
                        <span className="font-medium">{member.commission_pct}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Joined:</span>
                        <span className="font-medium">{member.joinDate}</span>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/team/view/${member.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      {hasPermission('team', 'edit') && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate(`/admin/team/edit/${member.id}`)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      )}
                      {hasPermission('team', 'delete') && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setMemberToDelete(member)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </Card>
                ))
              ) : (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">No Team Members Found</h3>
                  <p className="text-gray-500">No team members match your search. Try a different query.</p>
                </div>
              )}
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Team Member</TableHead>
                    <TableHead className="hidden lg:table-cell">Contact</TableHead>
                    <TableHead className="text-center hidden sm:table-cell">Refer Code</TableHead>
                    <TableHead className="text-center hidden sm:table-cell">Clients</TableHead>
                    <TableHead className="text-center">Commission</TableHead>
                    <TableHead className="hidden lg:table-cell">Status</TableHead>
                    <TableHead className="hidden xl:table-cell">Join Date</TableHead>
                    <TableHead><span className="sr-only">Actions</span></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    Array.from({ length: 5 }).map((_, i) => <SkeletonRow key={i} />)
                  ) : filteredMembers.length > 0 ? (
                    filteredMembers.map((member) => (
                      <TableRow key={member.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 rounded-full bg-primary/10 text-primary flex items-center justify-center font-bold">
                              {member.name.charAt(0)}
                            </div>
                            <div className="min-w-0">
                              <p className="font-semibold text-gray-800 truncate">{member.name}</p>
                              <p className="text-xs text-gray-500 truncate">{member.refer_code || 'No code'}</p>
                              <div className="lg:hidden">
                                <p className="text-xs text-gray-600 truncate">{member.email}</p>
                                <Badge variant={member.status === 'Active' ? 'default' : 'outline'} className="text-xs mt-1">
                                  {member.status}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <p className="text-sm font-medium text-gray-700 truncate">{member.email}</p>
                          <p className="text-xs text-gray-500">{member.phone}</p>
                        </TableCell>
                        <TableCell className="text-center hidden sm:table-cell">
                          <Badge variant="outline" className="font-mono text-xs">{member.refer_code}</Badge>
                        </TableCell>
                        <TableCell className="text-center hidden sm:table-cell">
                          <Badge variant="secondary" className="font-medium">{member.totalClients}</Badge>
                        </TableCell>
                        <TableCell className="text-center">
                          <span className="font-mono text-sm font-medium">{member.commission_pct}%</span>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <Badge variant={member.status === 'Active' ? 'default' : 'outline'} className="capitalize">
                            <span className={`h-2 w-2 mr-2 rounded-full ${member.status === 'Active' ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                            {member.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden xl:table-cell">
                          <span className="text-sm text-gray-600">{member.joinDate}</span>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => navigate(`/admin/team/view/${member.id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => navigate(`/admin/team/edit/${member.id}`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => setMemberToDelete(member)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-48 text-center">
                        <div className="flex flex-col items-center justify-center gap-4">
                          <Users className="h-12 w-12 text-gray-400" />
                          <h3 className="text-xl font-semibold text-gray-700">No Team Members Found</h3>
                          <p className="text-gray-500">No team members match your search. Try a different query.</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      <AlertDialog open={!!memberToDelete} onOpenChange={() => setMemberToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the team member "{memberToDelete?.name}" as deleted. This is a soft delete and the data can be recovered.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteMember} className="bg-red-600 hover:bg-red-700">
              Yes, Delete Member
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default TeamManagement;
