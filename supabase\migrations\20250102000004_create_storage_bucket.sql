-- Create storage bucket for client photos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'client-photos',
  'client-photos', 
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Create storage policy for client photos
CREATE POLICY "Allow public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'client-photos');

CREATE POLICY "Allow authenticated users to upload" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'client-photos' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow users to update their uploads" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'client-photos' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow users to delete their uploads" ON storage.objects
FOR DELETE USING (
  bucket_id = 'client-photos' 
  AND auth.role() = 'authenticated'
);