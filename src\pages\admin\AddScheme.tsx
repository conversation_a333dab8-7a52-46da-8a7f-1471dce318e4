import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermission";

const AddScheme = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    min_investment: "",
    max_investment: "",
    tenure_months: "",
    monthly_interest_pct: "",
    annual_interest_pct: "",
    commission_pct: "",
    lock_in_period: "",
    scheme_type: "fixed"
  });
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    if (isEditing && id) {
      fetchScheme();
    }
  }, [isEditing, id]);

  const fetchScheme = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      setFormData({
        name: data.name || '',
        description: data.description || '',
        min_investment: data.min_investment?.toString() || '',
        max_investment: data.max_investment?.toString() || '',
        tenure_months: data.tenure_months?.toString() || '',
        monthly_interest_pct: data.monthly_interest_pct?.toString() || '',
        annual_interest_pct: data.annual_interest_pct?.toString() || '',
        commission_pct: data.commission_pct?.toString() || '',
        lock_in_period: data.lock_in_period?.toString() || '',
        scheme_type: data.scheme_type || 'fixed'
      });
    } catch (error: any) {
      toast.error('Failed to fetch scheme: ' + error.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const schemeData = {
        name: formData.name,
        description: formData.description,
        min_investment: parseFloat(formData.min_investment),
        max_investment: parseFloat(formData.max_investment),
        tenure_months: parseInt(formData.tenure_months),
        monthly_interest_pct: parseFloat(formData.monthly_interest_pct),
        annual_interest_pct: parseFloat(formData.annual_interest_pct),
        commission_pct: parseFloat(formData.commission_pct) || 0,
        lock_in_period: parseInt(formData.lock_in_period) || 0,
        scheme_type: formData.scheme_type
      };

      if (isEditing) {
        const { error } = await supabase
          .from('schemes')
          .update(schemeData)
          .eq('id', id);

        if (error) throw error;
        toast.success("Scheme updated successfully!");
      } else {
        const { error } = await supabase
          .from('schemes')
          .insert(schemeData);

        if (error) throw error;
        toast.success("Scheme created successfully!");
      }

      navigate("/admin/schemes");
    } catch (error: any) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} scheme: ` + error.message);
    } finally {
      setLoading(false);
    }
  };
   const requiredAction = isEditing ? 'edit' : 'add';
  if (permissionsLoading) {
      return <div>Loading permissions...</div>;
  }

  if (!hasPermission('schemes', requiredAction)) {
      return (
          <div className="p-8 text-center text-red-600">
              <h2 className="text-xl font-bold">Access Denied</h2>
              <p>You do not have permission to {requiredAction} schemes.</p>
          </div>
      );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className=" mx-auto space-y-8">
        <header className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/admin/schemes")}
              className="shadow-sm hover:shadow-md transition-shadow"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Schemes
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {isEditing ? 'Edit Scheme' : 'Add New Scheme'}
              </h1>
              <p className="text-muted-foreground mt-1">
                {isEditing ? 'Update scheme information' : 'Create a new investment scheme'}
              </p>
            </div>
          </div>
        </header>

        <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm">
          <div className="p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">1</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Basic Information</h3>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="name" className="text-sm font-medium text-gray-700">Scheme Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        placeholder="Enter scheme name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="description" className="text-sm font-medium text-gray-700">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        placeholder="Enter scheme description"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        rows={3}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="scheme_type" className="text-sm font-medium text-gray-700">Scheme Type</Label>
                      <Select value={formData.scheme_type} onValueChange={(value) => setFormData({ ...formData, scheme_type: value })}>
                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                          <SelectValue placeholder="Select scheme type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fixed">Fixed</SelectItem>
                          <SelectItem value="flexible">Flexible</SelectItem>
                          <SelectItem value="growth">Growth</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>

              {/* Investment Details */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">2</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Investment Details</h3>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="min_investment" className="text-sm font-medium text-gray-700">Minimum Investment (₹) *</Label>
                      <Input
                        id="min_investment"
                        type="number"
                        value={formData.min_investment}
                        onChange={(e) => setFormData({ ...formData, min_investment: e.target.value })}
                        placeholder="e.g., 50000"
                        className="border-gray-300 focus:border-green-500 focus:ring-green-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max_investment" className="text-sm font-medium text-gray-700">Maximum Investment (₹)</Label>
                      <Input
                        id="max_investment"
                        type="number"
                        value={formData.max_investment}
                        onChange={(e) => setFormData({ ...formData, max_investment: e.target.value })}
                        placeholder="e.g., 1000000"
                        className="border-gray-300 focus:border-green-500 focus:ring-green-500/20"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="tenure_months" className="text-sm font-medium text-gray-700">Tenure (Months) *</Label>
                      <Input
                        id="tenure_months"
                        type="number"
                        value={formData.tenure_months}
                        onChange={(e) => setFormData({ ...formData, tenure_months: e.target.value })}
                        placeholder="e.g., 24"
                        className="border-gray-300 focus:border-green-500 focus:ring-green-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lock_in_period" className="text-sm font-medium text-gray-700">Lock-in Period (Months)</Label>
                      <Input
                        id="lock_in_period"
                        type="number"
                        value={formData.lock_in_period}
                        onChange={(e) => setFormData({ ...formData, lock_in_period: e.target.value })}
                        placeholder="e.g., 6"
                        className="border-gray-300 focus:border-green-500 focus:ring-green-500/20"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Interest & Commission */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">3</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Interest & Commission</h3>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="monthly_interest_pct" className="text-sm font-medium text-gray-700">Monthly Interest (%) *</Label>
                      <Input
                        id="monthly_interest_pct"
                        type="number"
                        step="0.01"
                        value={formData.monthly_interest_pct}
                        onChange={(e) => setFormData({ ...formData, monthly_interest_pct: e.target.value })}
                        placeholder="e.g., 1.5"
                        className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="annual_interest_pct" className="text-sm font-medium text-gray-700">Annual Interest (%)</Label>
                      <Input
                        id="annual_interest_pct"
                        type="number"
                        step="0.01"
                        value={formData.annual_interest_pct}
                        onChange={(e) => setFormData({ ...formData, annual_interest_pct: e.target.value })}
                        placeholder="e.g., 18"
                        className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="commission_pct" className="text-sm font-medium text-gray-700">Commission (%)</Label>
                      <Input
                        id="commission_pct"
                        type="number"
                        step="0.01"
                        value={formData.commission_pct}
                        onChange={(e) => setFormData({ ...formData, commission_pct: e.target.value })}
                        placeholder="e.g., 2"
                        className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={() => navigate("/admin/schemes")}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (isEditing ? "Updating..." : "Creating...") : (isEditing ? "Update Scheme" : "Create Scheme")}
                </Button>
              </div>
            </form>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AddScheme;