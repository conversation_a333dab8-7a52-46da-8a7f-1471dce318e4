import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import {
  LayoutDashboard,
  Users,
  TrendingUp,
  Banknote,
  Building2,
  UserCheck,
  Calculator,
  PieChart,
  FileText,
  Bell,
  Settings,
  LogOut,
  Shield,
  Key
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermission";
import { useCompanySettings } from "@/hooks/useCompanySettings";

// Define the structure for all possible menu items with a 'resource' key
const menuItemsConfig = [
  // Base Items
  { icon: LayoutDashboard, label: "Dashboard", path: (role: string) => `/${role}`, resource: "dashboard_admin", roles: ['team_member', 'admin'] },
  { icon: LayoutDashboard, label: "Dashboard", path: (role: string) => `/${role}`, resource: "dashboard_team", roles: ['team_member', 'client'] },

  // Admin Items
  { icon: Users, label: "Clients", path: () => "/admin/clients", resource: "clients", roles: ['team_member', 'admin'] },
  { icon: TrendingUp, label: "Investments", path: () => "/admin/investments", resource: "investments", roles: ['team_member', 'admin'] },
  { icon: Banknote, label: "Schemes", path: () => "/admin/schemes", resource: "schemes", roles: ['team_member', 'admin'] },
  { icon: Building2, label: "Broker Investments", path: () => "/admin/broker-investments", resource: "broker_investments", roles: ['team_member', 'admin'] },
  { icon: UserCheck, label: "Brokers", path: () => "/admin/brokers", resource: "brokers", roles: ['team_member', 'admin'] },
  { icon: Users, label: "Team", path: () => "/admin/team", resource: "team", roles: ['team_member', 'admin'] },
  { icon: Calculator, label: "Calculator", path: () => "/admin/calculator", resource: "calculator", roles: ['team_member', 'admin'] },
  { icon: PieChart, label: "Portfolio", path: () => "/admin/portfolio", resource: "portfolio", roles: ['team_member', 'admin'] },
  { icon: FileText, label: "Blogs", path: () => "/admin/blogs", resource: "blogs", roles: ['team_member', 'admin'] },
  { icon: Bell, label: "Alerts", path: () => "/admin/alerts", resource: "alerts", roles: ['team_member', 'admin'] },
  { icon: Shield, label: "Roles", path: () => "/admin/roles", resource: "roles", roles: ['team_member', 'admin'] },
  { icon: Key, label: "Permissions", path: () => "/admin/permissions", resource: "permissions_management", roles: ['team_member', 'admin'] },
  // { icon: Settings, label: "Settings", path: () => "/admin/settings", resource: "settings", roles: ['admin'] },
  { icon: Building2, label: "Company Settings", path: () => "/admin/company-settings", resource: "settings", roles: ['team_member', 'admin'] },

  // Team Items
  { icon: Settings, label: "Settings", path: () => "/team/settings", resource: "settings", roles: ['team_member'] },

  // Client Items
  { icon: TrendingUp, label: "Investments", path: () => "/client/investments", resource: "investments", roles: ['client'] },
  { icon: PieChart, label: "Portfolio", path: () => "/client/portfolio", resource: "portfolio", roles: ['client'] },
  { icon: Settings, label: "Settings", path: () => "/client/settings", resource: "settings", roles: ['client'] },
];


interface AdminSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

const AdminSidebar = ({ isOpen = false, onClose }: AdminSidebarProps) => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const { canAccess } = usePermissions();
  const { settings } = useCompanySettings();

  // Filter the master list of items based on the user's role and their permissions
  const menuItems = menuItemsConfig.filter(item =>
    item.roles.includes(user?.role || 'client') && canAccess(item.resource)
  );

  return (
    <div className={`fixed left-0 top-0 h-full w-64 bg-card border-r border-border shadow-sm z-50 transform transition-transform duration-300 lg:translate-x-0 ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:static lg:transform-none`}>
      <div className="flex flex-col h-full">
        {/* Logo Section */}
        <div className="border-b border-border">
          <div className="flex items-center bg-black">
            {settings.company_logo_url ? (
              <img
                src={settings.company_logo_url}
                alt={settings.company_name}
                className=" w-full h-14 object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <Building2 className="h-8 w-8 text-primary" />
            )}
            <div>

            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 overflow-y-auto">
          <ul className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              // Path is now a function
              const path = typeof item.path === 'function' ? item.path(user?.role || 'client') : item.path;
              const isActive = location.pathname === path;

              return (
                <li key={item.label + path}>
                  <Link
                    to={path}
                    onClick={onClose}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 group",
                      "hover:bg-accent/50 hover:text-accent-foreground",
                      isActive
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                  >
                    <Icon className={cn(
                      "h-5 w-5 transition-colors",
                      isActive ? "text-primary-foreground" : "group-hover:text-foreground"
                    )} />
                    <span className="text-sm font-medium">{item.label}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default AdminSidebar;

