
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Calendar, User, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

const Blogs = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const categories = ["All", "Investment Tips", "Market Analysis", "Financial Planning", "Company News"];

  const blogPosts = [
    {
      id: 1,
      title: "5 Investment Strategies for 2024",
      excerpt: "Discover the top investment strategies that successful investors are using in 2024 to maximize their returns while minimizing risks.",
      content: "Full content here...",
      author: "<PERSON><PERSON>",
      date: "2024-01-15",
      category: "Investment Tips",
      image: "/api/placeholder/400/250",
      tags: ["Investment", "Strategy", "2024"],
      readTime: "5 min read"
    },
    {
      id: 2,
      title: "Understanding Market Volatility: A Beginner's Guide",
      excerpt: "Market volatility can be intimidating for new investors. Learn how to navigate volatile markets and turn uncertainty into opportunity.",
      content: "Full content here...",
      author: "Priya Sharma",
      date: "2024-01-20",
      category: "Market Analysis",
      image: "/api/placeholder/400/250",
      tags: ["Market", "Volatility", "Beginner"],
      readTime: "7 min read"
    },
    {
      id: 3,
      title: "Gold vs Silver Investment Plans: Which is Right for You?",
      excerpt: "Compare our Gold and Silver investment plans to understand which option aligns best with your financial goals and risk tolerance.",
      content: "Full content here...",
      author: "Amit Mehta",
      date: "2024-01-10",
      category: "Investment Tips",
      image: "/api/placeholder/400/250",
      tags: ["Gold Plan", "Silver Plan", "Comparison"],
      readTime: "6 min read"
    },
    {
      id: 4,
      title: "Building Your Emergency Fund: A Step-by-Step Guide",
      excerpt: "Learn why every investor needs an emergency fund and how to build one that protects your financial future.",
      content: "Full content here...",
      author: "Priya Sharma",
      date: "2024-01-25",
      category: "Financial Planning",
      image: "/api/placeholder/400/250",
      tags: ["Emergency Fund", "Savings", "Planning"],
      readTime: "8 min read"
    },
    {
      id: 5,
      title: "Care Capital's New Platinum Plan: Enhanced Returns for Long-term Investors",
      excerpt: "Introducing our new Platinum Plan with industry-leading returns and exclusive benefits for committed long-term investors.",
      content: "Full content here...",
      author: "Rajesh Kumar",
      date: "2024-01-30",
      category: "Company News",
      image: "/api/placeholder/400/250",
      tags: ["Platinum Plan", "New Launch", "Returns"],
      readTime: "4 min read"
    },
    {
      id: 6,
      title: "Tax-Efficient Investing: Maximize Your After-Tax Returns",
      excerpt: "Discover strategies to minimize your tax burden while maximizing your investment returns through smart tax planning.",
      content: "Full content here...",
      author: "Amit Mehta",
      date: "2024-02-01",
      category: "Financial Planning",
      image: "/api/placeholder/400/250",
      tags: ["Tax Planning", "Returns", "Strategy"],
      readTime: "9 min read"
    }
  ];

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === "All" || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Investment Insights & Market Updates
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Stay informed with expert analysis, investment tips, and market insights 
            from our team of financial professionals.
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-12 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-8">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
                <Input
                  className="pl-10"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">Featured Article</h2>
            <Card className="overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <div className="h-64 md:h-full bg-gradient-to-r from-primary/20 to-primary/40 flex items-center justify-center">
                    <span className="text-6xl">📈</span>
                  </div>
                </div>
                <div className="md:w-1/2 p-8">
                  <Badge className="mb-4">Featured</Badge>
                  <h3 className="text-2xl font-bold mb-4">{blogPosts[0].title}</h3>
                  <p className="text-muted-foreground mb-6">{blogPosts[0].excerpt}</p>
                  <div className="flex items-center text-sm text-muted-foreground mb-6">
                    <User className="h-4 w-4 mr-2" />
                    <span className="mr-4">{blogPosts[0].author}</span>
                    <Calendar className="h-4 w-4 mr-2" />
                    <span className="mr-4">{blogPosts[0].date}</span>
                    <span>{blogPosts[0].readTime}</span>
                  </div>
                  <Link to={`/blog/${blogPosts[0].id}`}>
                    <Button>
                      Read More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">Latest Articles</h2>
            
            {filteredPosts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.slice(1).map((post) => (
                  <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="h-48 bg-gradient-to-r from-primary/20 to-primary/40 flex items-center justify-center">
                      <span className="text-3xl">📊</span>
                    </div>
                    <CardHeader>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{post.category}</Badge>
                        <span className="text-xs text-muted-foreground">{post.readTime}</span>
                      </div>
                      <CardTitle className="line-clamp-2">{post.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground mb-4 line-clamp-3">{post.excerpt}</p>
                      <div className="flex items-center text-sm text-muted-foreground mb-4">
                        <User className="h-4 w-4 mr-1" />
                        <span className="mr-3">{post.author}</span>
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{post.date}</span>
                      </div>
                      <div className="flex flex-wrap gap-1 mb-4">
                        {post.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <Link to={`/blog/${post.id}`}>
                        <Button variant="outline" className="w-full">
                          Read Article
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold mb-2">No articles found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search terms or browse all categories.
                </p>
                <Button variant="outline" onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("All");
                }}>
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Stay Updated</h2>
            <p className="text-xl mb-8">
              Subscribe to our newsletter and never miss important market insights and investment tips.
            </p>
            <div className="flex flex-col md:flex-row gap-4 max-w-lg mx-auto">
              <Input
                placeholder="Enter your email"
                className="flex-1 bg-white text-black"
              />
              <Button className="bg-white text-primary hover:bg-gray-100">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blogs;
