
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calculator, DollarSign, TrendingUp, Calendar } from "lucide-react";

const TeamCalculator = () => {
  const [principal, setPrincipal] = useState("");
  const [rate, setRate] = useState("");
  const [time, setTime] = useState("");
  const [compoundFreq, setCompoundFreq] = useState("");
  const [result, setResult] = useState(0);

  const calculateInterest = () => {
    const p = parseFloat(principal);
    const r = parseFloat(rate) / 100;
    const t = parseFloat(time);
    const n = parseFloat(compoundFreq) || 1;

    if (p && r && t) {
      const amount = p * Math.pow((1 + r/n), n * t);
      setResult(amount);
    }
  };

  const schemes = [
    { name: "Gold Plan", rate: 12, minAmount: 50000, tenure: "12-24 months" },
    { name: "Silver Plan", rate: 10, minAmount: 25000, tenure: "6-18 months" },
    { name: "Platinum Plan", rate: 15, minAmount: 100000, tenure: "24-36 months" },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Investment Calculator</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="mr-2 h-5 w-5" />
              Interest Calculator
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="principal">Principal Amount (₹)</Label>
              <Input
                id="principal"
                type="number"
                placeholder="Enter principal amount"
                value={principal}
                onChange={(e) => setPrincipal(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="rate">Interest Rate (%)</Label>
              <Input
                id="rate"
                type="number"
                placeholder="Enter interest rate"
                value={rate}
                onChange={(e) => setRate(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="time">Time Period (years)</Label>
              <Input
                id="time"
                type="number"
                placeholder="Enter time period"
                value={time}
                onChange={(e) => setTime(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="compound">Compound Frequency</Label>
              <Select value={compoundFreq} onValueChange={setCompoundFreq}>
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Annually</SelectItem>
                  <SelectItem value="2">Semi-annually</SelectItem>
                  <SelectItem value="4">Quarterly</SelectItem>
                  <SelectItem value="12">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={calculateInterest} className="w-full">
              Calculate
            </Button>
            
            {result > 0 && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold text-lg">Result:</h3>
                <p className="text-2xl font-bold text-primary">₹{result.toLocaleString('en-IN', { maximumFractionDigits: 2 })}</p>
                <p className="text-sm text-muted-foreground">
                  Interest Earned: ₹{(result - parseFloat(principal)).toLocaleString('en-IN', { maximumFractionDigits: 2 })}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Available Schemes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {schemes.map((scheme, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold">{scheme.name}</h3>
                    <Badge className="bg-green-100 text-green-800">{scheme.rate}% p.a.</Badge>
                  </div>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      Min Amount: ₹{scheme.minAmount.toLocaleString()}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Tenure: {scheme.tenure}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      setRate(scheme.rate.toString());
                      setPrincipal(scheme.minAmount.toString());
                    }}
                  >
                    Use This Scheme
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Investment Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <h3 className="font-semibold text-lg">1 Year Investment</h3>
              <p className="text-2xl font-bold text-primary">₹55,000</p>
              <p className="text-sm text-muted-foreground">on ₹50,000 @ 10%</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <h3 className="font-semibold text-lg">2 Year Investment</h3>
              <p className="text-2xl font-bold text-primary">₹60,500</p>
              <p className="text-sm text-muted-foreground">on ₹50,000 @ 10%</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <h3 className="font-semibold text-lg">3 Year Investment</h3>
              <p className="text-2xl font-bold text-primary">₹66,550</p>
              <p className="text-sm text-muted-foreground">on ₹50,000 @ 10%</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamCalculator;
