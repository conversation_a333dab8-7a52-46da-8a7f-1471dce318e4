
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Shield, Target, Users, Award } from "lucide-react";

const AboutUs = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            About Care Capital
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Your trusted partner in building financial security through innovative investment solutions 
            and personalized wealth management services.
          </p>
        </div>
      </section>

      {/* Company Story */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Our Story</h2>
            <div className="text-lg text-muted-foreground space-y-6">
              <p>
                Founded in 2015, Care Capital began with a simple yet powerful vision: to democratize wealth creation 
                and make quality investment opportunities accessible to everyone, regardless of their financial background.
              </p>
              <p>
                What started as a small team of passionate financial experts has grown into a trusted investment platform 
                serving thousands of clients across India. Our journey has been marked by innovation, transparency, and 
                an unwavering commitment to our clients' financial success.
              </p>
              <p>
                Today, we pride ourselves on being more than just an investment company. We are financial partners, 
                educators, and advocates for our clients' dreams and aspirations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Our Core Values</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="text-center">
                <Shield className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Trust & Security</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Your investments are secured with bank-grade security and regulatory compliance
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Target className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Goal-Oriented</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Every investment strategy is tailored to help you achieve your specific financial goals
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Users className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Client-Centric</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  Your success is our success. We prioritize your interests above everything else
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Award className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Excellence</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground">
                  We strive for excellence in every service we provide and relationship we build
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Our Vision</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg text-muted-foreground">
                  To become India's most trusted and innovative financial partner, empowering millions 
                  of individuals and families to achieve their financial dreams through smart investment 
                  solutions and exceptional service.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Our Mission</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg text-muted-foreground">
                  To provide accessible, transparent, and profitable investment opportunities while 
                  educating our clients about financial literacy and helping them make informed 
                  decisions for a secure financial future.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Leadership Team</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardContent className="text-center pt-6">
                <div className="w-24 h-24 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-primary">RK</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Rajesh Kumar</h3>
                <p className="text-muted-foreground mb-4">Founder & CEO</p>
                <p className="text-sm text-muted-foreground">
                  20+ years in financial services. Previously worked with leading investment banks 
                  and wealth management firms.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="text-center pt-6">
                <div className="w-24 h-24 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-primary">PS</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Priya Sharma</h3>
                <p className="text-muted-foreground mb-4">Chief Investment Officer</p>
                <p className="text-sm text-muted-foreground">
                  Expert in portfolio management with 15+ years experience in equity and debt markets. 
                  MBA from IIM Bangalore.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="text-center pt-6">
                <div className="w-24 h-24 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-primary">AM</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Amit Mehta</h3>
                <p className="text-muted-foreground mb-4">Head of Operations</p>
                <p className="text-sm text-muted-foreground">
                  Operations and technology expert ensuring smooth platform functionality and 
                  exceptional client experience.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Our Achievements</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">5000+</div>
              <p className="text-muted-foreground">Happy Clients</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">₹500Cr+</div>
              <p className="text-muted-foreground">Assets Under Management</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">12%</div>
              <p className="text-muted-foreground">Average Annual Returns</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">98%</div>
              <p className="text-muted-foreground">Client Satisfaction Rate</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Start Your Investment Journey?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied clients who trust Care Capital with their financial future.
          </p>
          <Button size="lg" className="bg-white text-primary hover:bg-gray-100">
            Get Started Today
          </Button>
        </div>
      </section>
    </div>
  );
};

export default AboutUs;
